var AIModelsBridge=function(e){"use strict";var t;e.ErrorCode=void 0,(t=e.ErrorCode||(e.ErrorCode={})).UNKNOWN_ERROR="unknown_error",t.NETWORK_ERROR="network_error",t.UNAUTHORIZED="unauthorized",t.SERVICE_UNAVAILABLE="service_unavailable",t.MISSING_API_KEY="missing_api_key",t.MISSING_HOST_PERMISSION="missing_host_permission",t.CONVERSATION_LIMIT="conversation_limit",t.CONTENT_FILTERED="content_filtered",t.INVALID_REQUEST="invalid_request",t.INVALID_API_KEY="invalid_api_key",t.INVALID_THREAD_ID="invalid_thread_id",t.INVALID_METADATA="invalid_metadata",t.INVALID_MESSAGE_ID="invalid_message_id",t.INVALID_MODEL="invalid_model",t.INVALID_IMAGE_TYPE="invalid_image_type",t.INVALID_IMAGE_CONTENT="invalid_image_content",t.UPLOAD_FAILED="upload_failed",t.UPLOAD_TIMEOUT="upload_timeout",t.UPLOAD_SIZE_EXCEEDED="upload_size_exceeded",t.UPLOAD_TYPE_EXCEEDED="upload_type_exceeded",t.UPLOAD_AMOUNT_EXCEEDED="upload_amount_exceeded",t.UPLOAD_TYPE_NOT_SUPPORTED="upload_type_not_supported",t.RATE_LIMIT_EXCEEDED="rate_limit_exceeded",t.METADATA_INITIALIZATION_ERROR="metadata_initialization_error",t.FEATURE_NOT_SUPPORTED="feature_not_supported",t.RESPONSE_PARSING_ERROR="response_parsing_error",t.POW_CHALLENGE_FAILED="pow_challenge_failed";class r extends Error{constructor(t,r=e.ErrorCode.UNKNOWN_ERROR){super(t),this.code=r,this.name="AIModelError"}}function a(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}var o,s={exports:{}};var n=a((o||(o=1,"undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self&&self,function(e){if(!globalThis.chrome?.runtime?.id)throw new Error("This script should only be loaded in a browser extension.");if(void 0===globalThis.browser||Object.getPrototypeOf(globalThis.browser)!==Object.prototype){const t="The message port closed before a response was received.",r=e=>{const r={alarms:{clear:{minArgs:0,maxArgs:1},clearAll:{minArgs:0,maxArgs:0},get:{minArgs:0,maxArgs:1},getAll:{minArgs:0,maxArgs:0}},bookmarks:{create:{minArgs:1,maxArgs:1},get:{minArgs:1,maxArgs:1},getChildren:{minArgs:1,maxArgs:1},getRecent:{minArgs:1,maxArgs:1},getSubTree:{minArgs:1,maxArgs:1},getTree:{minArgs:0,maxArgs:0},move:{minArgs:2,maxArgs:2},remove:{minArgs:1,maxArgs:1},removeTree:{minArgs:1,maxArgs:1},search:{minArgs:1,maxArgs:1},update:{minArgs:2,maxArgs:2}},browserAction:{disable:{minArgs:0,maxArgs:1,fallbackToNoCallback:!0},enable:{minArgs:0,maxArgs:1,fallbackToNoCallback:!0},getBadgeBackgroundColor:{minArgs:1,maxArgs:1},getBadgeText:{minArgs:1,maxArgs:1},getPopup:{minArgs:1,maxArgs:1},getTitle:{minArgs:1,maxArgs:1},openPopup:{minArgs:0,maxArgs:0},setBadgeBackgroundColor:{minArgs:1,maxArgs:1,fallbackToNoCallback:!0},setBadgeText:{minArgs:1,maxArgs:1,fallbackToNoCallback:!0},setIcon:{minArgs:1,maxArgs:1},setPopup:{minArgs:1,maxArgs:1,fallbackToNoCallback:!0},setTitle:{minArgs:1,maxArgs:1,fallbackToNoCallback:!0}},browsingData:{remove:{minArgs:2,maxArgs:2},removeCache:{minArgs:1,maxArgs:1},removeCookies:{minArgs:1,maxArgs:1},removeDownloads:{minArgs:1,maxArgs:1},removeFormData:{minArgs:1,maxArgs:1},removeHistory:{minArgs:1,maxArgs:1},removeLocalStorage:{minArgs:1,maxArgs:1},removePasswords:{minArgs:1,maxArgs:1},removePluginData:{minArgs:1,maxArgs:1},settings:{minArgs:0,maxArgs:0}},commands:{getAll:{minArgs:0,maxArgs:0}},contextMenus:{remove:{minArgs:1,maxArgs:1},removeAll:{minArgs:0,maxArgs:0},update:{minArgs:2,maxArgs:2}},cookies:{get:{minArgs:1,maxArgs:1},getAll:{minArgs:1,maxArgs:1},getAllCookieStores:{minArgs:0,maxArgs:0},remove:{minArgs:1,maxArgs:1},set:{minArgs:1,maxArgs:1}},devtools:{inspectedWindow:{eval:{minArgs:1,maxArgs:2,singleCallbackArg:!1}},panels:{create:{minArgs:3,maxArgs:3,singleCallbackArg:!0},elements:{createSidebarPane:{minArgs:1,maxArgs:1}}}},downloads:{cancel:{minArgs:1,maxArgs:1},download:{minArgs:1,maxArgs:1},erase:{minArgs:1,maxArgs:1},getFileIcon:{minArgs:1,maxArgs:2},open:{minArgs:1,maxArgs:1,fallbackToNoCallback:!0},pause:{minArgs:1,maxArgs:1},removeFile:{minArgs:1,maxArgs:1},resume:{minArgs:1,maxArgs:1},search:{minArgs:1,maxArgs:1},show:{minArgs:1,maxArgs:1,fallbackToNoCallback:!0}},extension:{isAllowedFileSchemeAccess:{minArgs:0,maxArgs:0},isAllowedIncognitoAccess:{minArgs:0,maxArgs:0}},history:{addUrl:{minArgs:1,maxArgs:1},deleteAll:{minArgs:0,maxArgs:0},deleteRange:{minArgs:1,maxArgs:1},deleteUrl:{minArgs:1,maxArgs:1},getVisits:{minArgs:1,maxArgs:1},search:{minArgs:1,maxArgs:1}},i18n:{detectLanguage:{minArgs:1,maxArgs:1},getAcceptLanguages:{minArgs:0,maxArgs:0}},identity:{launchWebAuthFlow:{minArgs:1,maxArgs:1}},idle:{queryState:{minArgs:1,maxArgs:1}},management:{get:{minArgs:1,maxArgs:1},getAll:{minArgs:0,maxArgs:0},getSelf:{minArgs:0,maxArgs:0},setEnabled:{minArgs:2,maxArgs:2},uninstallSelf:{minArgs:0,maxArgs:1}},notifications:{clear:{minArgs:1,maxArgs:1},create:{minArgs:1,maxArgs:2},getAll:{minArgs:0,maxArgs:0},getPermissionLevel:{minArgs:0,maxArgs:0},update:{minArgs:2,maxArgs:2}},pageAction:{getPopup:{minArgs:1,maxArgs:1},getTitle:{minArgs:1,maxArgs:1},hide:{minArgs:1,maxArgs:1,fallbackToNoCallback:!0},setIcon:{minArgs:1,maxArgs:1},setPopup:{minArgs:1,maxArgs:1,fallbackToNoCallback:!0},setTitle:{minArgs:1,maxArgs:1,fallbackToNoCallback:!0},show:{minArgs:1,maxArgs:1,fallbackToNoCallback:!0}},permissions:{contains:{minArgs:1,maxArgs:1},getAll:{minArgs:0,maxArgs:0},remove:{minArgs:1,maxArgs:1},request:{minArgs:1,maxArgs:1}},runtime:{getBackgroundPage:{minArgs:0,maxArgs:0},getPlatformInfo:{minArgs:0,maxArgs:0},openOptionsPage:{minArgs:0,maxArgs:0},requestUpdateCheck:{minArgs:0,maxArgs:0},sendMessage:{minArgs:1,maxArgs:3},sendNativeMessage:{minArgs:2,maxArgs:2},setUninstallURL:{minArgs:1,maxArgs:1}},sessions:{getDevices:{minArgs:0,maxArgs:1},getRecentlyClosed:{minArgs:0,maxArgs:1},restore:{minArgs:0,maxArgs:1}},storage:{local:{clear:{minArgs:0,maxArgs:0},get:{minArgs:0,maxArgs:1},getBytesInUse:{minArgs:0,maxArgs:1},remove:{minArgs:1,maxArgs:1},set:{minArgs:1,maxArgs:1}},managed:{get:{minArgs:0,maxArgs:1},getBytesInUse:{minArgs:0,maxArgs:1}},sync:{clear:{minArgs:0,maxArgs:0},get:{minArgs:0,maxArgs:1},getBytesInUse:{minArgs:0,maxArgs:1},remove:{minArgs:1,maxArgs:1},set:{minArgs:1,maxArgs:1}}},tabs:{captureVisibleTab:{minArgs:0,maxArgs:2},create:{minArgs:1,maxArgs:1},detectLanguage:{minArgs:0,maxArgs:1},discard:{minArgs:0,maxArgs:1},duplicate:{minArgs:1,maxArgs:1},executeScript:{minArgs:1,maxArgs:2},get:{minArgs:1,maxArgs:1},getCurrent:{minArgs:0,maxArgs:0},getZoom:{minArgs:0,maxArgs:1},getZoomSettings:{minArgs:0,maxArgs:1},goBack:{minArgs:0,maxArgs:1},goForward:{minArgs:0,maxArgs:1},highlight:{minArgs:1,maxArgs:1},insertCSS:{minArgs:1,maxArgs:2},move:{minArgs:2,maxArgs:2},query:{minArgs:1,maxArgs:1},reload:{minArgs:0,maxArgs:2},remove:{minArgs:1,maxArgs:1},removeCSS:{minArgs:1,maxArgs:2},sendMessage:{minArgs:2,maxArgs:3},setZoom:{minArgs:1,maxArgs:2},setZoomSettings:{minArgs:1,maxArgs:2},update:{minArgs:1,maxArgs:2}},topSites:{get:{minArgs:0,maxArgs:0}},webNavigation:{getAllFrames:{minArgs:1,maxArgs:1},getFrame:{minArgs:1,maxArgs:1}},webRequest:{handlerBehaviorChanged:{minArgs:0,maxArgs:0}},windows:{create:{minArgs:0,maxArgs:1},get:{minArgs:1,maxArgs:2},getAll:{minArgs:0,maxArgs:1},getCurrent:{minArgs:0,maxArgs:1},getLastFocused:{minArgs:0,maxArgs:1},remove:{minArgs:1,maxArgs:1},update:{minArgs:2,maxArgs:2}}};if(0===Object.keys(r).length)throw new Error("api-metadata.json has not been included in browser-polyfill");class a extends WeakMap{constructor(e,t=void 0){super(t),this.createItem=e}get(e){return this.has(e)||this.set(e,this.createItem(e)),super.get(e)}}const o=e=>e&&"object"==typeof e&&"function"==typeof e.then,s=(t,r)=>(...a)=>{e.runtime.lastError?t.reject(new Error(e.runtime.lastError.message)):r.singleCallbackArg||a.length<=1&&!1!==r.singleCallbackArg?t.resolve(a[0]):t.resolve(a)},n=e=>1==e?"argument":"arguments",i=(e,t)=>function(r,...a){if(a.length<t.minArgs)throw new Error(`Expected at least ${t.minArgs} ${n(t.minArgs)} for ${e}(), got ${a.length}`);if(a.length>t.maxArgs)throw new Error(`Expected at most ${t.maxArgs} ${n(t.maxArgs)} for ${e}(), got ${a.length}`);return new Promise((o,n)=>{if(t.fallbackToNoCallback)try{r[e](...a,s({resolve:o,reject:n},t))}catch(s){console.warn(`${e} API method doesn't seem to support the callback parameter, falling back to call it without a callback: `,s),r[e](...a),t.fallbackToNoCallback=!1,t.noCallback=!0,o()}else t.noCallback?(r[e](...a),o()):r[e](...a,s({resolve:o,reject:n},t))})},d=(e,t,r)=>new Proxy(t,{apply:(t,a,o)=>r.call(a,e,...o)});let l=Function.call.bind(Object.prototype.hasOwnProperty);const c=(e,t={},r={})=>{let a=Object.create(null),o={has:(t,r)=>r in e||r in a,get(o,s,n){if(s in a)return a[s];if(!(s in e))return;let h=e[s];if("function"==typeof h)if("function"==typeof t[s])h=d(e,e[s],t[s]);else if(l(r,s)){let t=i(s,r[s]);h=d(e,e[s],t)}else h=h.bind(e);else if("object"==typeof h&&null!==h&&(l(t,s)||l(r,s)))h=c(h,t[s],r[s]);else{if(!l(r,"*"))return Object.defineProperty(a,s,{configurable:!0,enumerable:!0,get:()=>e[s],set(t){e[s]=t}}),h;h=c(h,t[s],r["*"])}return a[s]=h,h},set:(t,r,o,s)=>(r in a?a[r]=o:e[r]=o,!0),defineProperty:(e,t,r)=>Reflect.defineProperty(a,t,r),deleteProperty:(e,t)=>Reflect.deleteProperty(a,t)},s=Object.create(e);return new Proxy(s,o)},h=e=>({addListener(t,r,...a){t.addListener(e.get(r),...a)},hasListener:(t,r)=>t.hasListener(e.get(r)),removeListener(t,r){t.removeListener(e.get(r))}}),u=new a(e=>"function"!=typeof e?e:function(t){const r=c(t,{},{getContent:{minArgs:0,maxArgs:0}});e(r)}),g=new a(e=>"function"!=typeof e?e:function(t,r,a){let s,n,i=!1,d=new Promise(e=>{s=function(t){i=!0,e(t)}});try{n=e(t,r,s)}catch(e){n=Promise.reject(e)}const l=!0!==n&&o(n);if(!0!==n&&!l&&!i)return!1;const c=e=>{e.then(e=>{a(e)},e=>{let t;t=e&&(e instanceof Error||"string"==typeof e.message)?e.message:"An unexpected error occurred",a({__mozWebExtensionPolyfillReject__:!0,message:t})}).catch(e=>{console.error("Failed to send onMessage rejected reply",e)})};return c(l?n:d),!0}),m=({reject:r,resolve:a},o)=>{e.runtime.lastError?e.runtime.lastError.message===t?a():r(new Error(e.runtime.lastError.message)):o&&o.__mozWebExtensionPolyfillReject__?r(new Error(o.message)):a(o)},p=(e,t,r,...a)=>{if(a.length<t.minArgs)throw new Error(`Expected at least ${t.minArgs} ${n(t.minArgs)} for ${e}(), got ${a.length}`);if(a.length>t.maxArgs)throw new Error(`Expected at most ${t.maxArgs} ${n(t.maxArgs)} for ${e}(), got ${a.length}`);return new Promise((e,t)=>{const o=m.bind(null,{resolve:e,reject:t});a.push(o),r.sendMessage(...a)})},E={devtools:{network:{onRequestFinished:h(u)}},runtime:{onMessage:h(g),onMessageExternal:h(g),sendMessage:p.bind(null,"sendMessage",{minArgs:1,maxArgs:3})},tabs:{sendMessage:p.bind(null,"sendMessage",{minArgs:2,maxArgs:3})}},f={clear:{minArgs:1,maxArgs:1},get:{minArgs:1,maxArgs:1},set:{minArgs:1,maxArgs:1}};return r.privacy={network:{"*":f},services:{"*":f},websites:{"*":f}},c(e,E,r)};e.exports=r(chrome)}else e.exports=globalThis.browser}(s)),s.exports));let i;const d=new Uint8Array(16);function l(){if(!i&&(i="undefined"!=typeof crypto&&crypto.getRandomValues&&crypto.getRandomValues.bind(crypto),!i))throw new Error("crypto.getRandomValues() not supported. See https://github.com/uuidjs/uuid#getrandomvalues-not-supported");return i(d)}const c=[];for(let e=0;e<256;++e)c.push((e+256).toString(16).slice(1));var h={randomUUID:"undefined"!=typeof crypto&&crypto.randomUUID&&crypto.randomUUID.bind(crypto)};function u(e,t,r){if(h.randomUUID&&!e)return h.randomUUID();const a=(e=e||{}).random||(e.rng||l)();return a[6]=15&a[6]|64,a[8]=63&a[8]|128,function(e,t=0){return c[e[t+0]]+c[e[t+1]]+c[e[t+2]]+c[e[t+3]]+"-"+c[e[t+4]]+c[e[t+5]]+"-"+c[e[t+6]]+c[e[t+7]]+"-"+c[e[t+8]]+c[e[t+9]]+"-"+c[e[t+10]]+c[e[t+11]]+c[e[t+12]]+c[e[t+13]]+c[e[t+14]]+c[e[t+15]]}(a)}class g{constructor(){this.baseUrl="",this.defaultModel=""}async sendMessage(t,a={onEvent:()=>{}}){try{let e="";return await this.doSendMessage({prompt:t,images:a.images,signal:a.signal,mode:a.mode,model:a.model,style_key:a.style_key,searchFocus:a.searchFocus,searchSources:a.searchSources,searchEnabled:a.searchEnabled,onEvent:t=>{"UPDATE_ANSWER"===t.type&&(e=t.data.text,a.onEvent({type:"UPDATE_ANSWER",data:t.data})),"DONE"===t.type&&a.onEvent({type:"DONE",data:t.data}),"SUGGESTED_RESPONSES"===t.type&&a.onEvent({type:"SUGGESTED_RESPONSES",data:t.data}),"TITLE_UPDATE"===t.type&&a.onEvent({type:"TITLE_UPDATE",data:t.data}),"ERROR"===t.type&&a.onEvent({type:"ERROR",error:t.error})}}),e}catch(t){a.onEvent({type:"ERROR",error:t instanceof r?t:new r(t instanceof Error?t.message:String(t),t instanceof r?t.code:e.ErrorCode.UNKNOWN_ERROR)}),this.handleModelError("Error sending message",t instanceof r?t.code:void 0,a,t)}}async getAllThreads(){try{return(await n.storage.local.get(g.THREADS_STORAGE_KEY))[g.THREADS_STORAGE_KEY]||[]}catch(e){return console.error("Failed to load threads from storage:",e),[]}}async saveThreadsToStorage(e){try{await n.storage.local.set({[g.THREADS_STORAGE_KEY]:e})}catch(e){console.error("Failed to save threads to storage:",e)}}getCurrentThread(){return this.currentThread}async loadThread(t){const r=(await this.getAllThreads()).find(e=>e.id===t);if(!r)return this.handleModelError("Thread not found",e.ErrorCode.INVALID_THREAD_ID);this.currentThread=r}async saveThread(){if(!this.currentThread)return this.handleModelError("No active thread to save",e.ErrorCode.INVALID_REQUEST);const t=await this.getAllThreads(),r=t.findIndex(e=>e.id===this.currentThread.id);-1!==r?t[r]=this.currentThread:t.push(this.currentThread),await this.saveThreadsToStorage(t)}async deleteThread(e,t=!0){const r=await this.getAllThreads();await this.saveThreadsToStorage(r.filter(t=>t.id!==e)),this.currentThread?.id===e&&t&&this.initNewThread()}createMessage(e,t){return{id:u(),role:e,content:t,timestamp:Date.now()}}getBaseUrl(){return this.baseUrl}handleModelError(t,a,o,s){!a&&s instanceof r?a=s.code:a||(s instanceof Error&&("AbortError"===s.name?a=e.ErrorCode.RATE_LIMIT_EXCEEDED:s.message.includes("network")||s.message.includes("connection")?a=e.ErrorCode.NETWORK_ERROR:s.message.includes("permission")||s.message.includes("unauthorized")?a=e.ErrorCode.UNAUTHORIZED:s.message.includes("timeout")&&(a=e.ErrorCode.SERVICE_UNAVAILABLE)),a=a||e.ErrorCode.UNKNOWN_ERROR);const n=s?s instanceof Error?s.message:String(s):"",i=new r(n?`${n} - ${t}`:t,a);throw s&&"cause"in Error&&Object.assign(i,{cause:s}),o?.onEvent&&o.onEvent({type:"ERROR",error:i}),console.error("AI model error:",i),i}async shareConversation(t){return this.handleModelError(`Sharing is not supported by the ${this.getName()} model`,e.ErrorCode.FEATURE_NOT_SUPPORTED)}}g.THREADS_STORAGE_KEY="chat_threads";const m="auth_token_cache_";async function p(e){const t={origins:[e]};try{return!!await n.permissions.contains(t)||(console.log(`Requesting host permission for: ${e}`),await n.permissions.request(t))}catch(e){return console.error("Error requesting permissions:",e),!1}}async function E(e,t,r,a,o=!1){console.log(`[${e} Auth Wrapper] Requesting token from background (forceRefresh: ${o})...`);try{const s=await n.runtime.sendMessage({type:"GET_AUTH_TOKEN_FROM_WEBSITE",payload:{serviceName:e,targetUrl:t,urlPattern:r,extractorName:a,forceNewTab:o}});if(console.log(`[${e} Auth Wrapper] Received response from background:`,s),s?.success)return s.token||null;{const t=s?.error||`Unknown error from background script for ${e}`;throw console.error(`[${e} Auth Wrapper] Background script failed: ${t}`),new Error(t)}}catch(t){if(console.error(`[${e} Auth Wrapper] Error communicating with background script:`,t),t instanceof Error&&(t.message.includes("Could not establish connection")||t.message.includes("Receiving end does not exist"))){const e="Background service communication error. Is the extension enabled/reloaded? Check background script logs.";throw console.error(e),new Error(e)}throw t}}function f(e,t,r,a){var o,s=arguments.length,n=s<3?t:null===a?a=Object.getOwnPropertyDescriptor(t,r):a;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)n=Reflect.decorate(e,t,r,a);else for(var i=e.length-1;i>=0;i--)(o=e[i])&&(n=(s<3?o(n):s>3?o(t,r,n):o(t,r))||n);return s>3&&n&&Object.defineProperty(t,r,n),n}function A(e,t){if("object"==typeof Reflect&&"function"==typeof Reflect.metadata)return Reflect.metadata(e,t)}"function"==typeof SuppressedError&&SuppressedError;const y=/"(?:_|\\u0{2}5[Ff]){2}(?:p|\\u0{2}70)(?:r|\\u0{2}72)(?:o|\\u0{2}6[Ff])(?:t|\\u0{2}74)(?:o|\\u0{2}6[Ff])(?:_|\\u0{2}5[Ff]){2}"\s*:/,_=/"(?:c|\\u0063)(?:o|\\u006[Ff])(?:n|\\u006[Ee])(?:s|\\u0073)(?:t|\\u0074)(?:r|\\u0072)(?:u|\\u0075)(?:c|\\u0063)(?:t|\\u0074)(?:o|\\u006[Ff])(?:r|\\u0072)"\s*:/,T=/^\s*["[{]|^\s*-?\d{1,16}(\.\d{1,17})?([Ee][+-]?\d+)?\s*$/;function w(e,t){if(!("__proto__"===e||"constructor"===e&&t&&"object"==typeof t&&"prototype"in t))return t;!function(e){console.warn(`[destr] Dropping "${e}" key to prevent prototype pollution.`)}(e)}function v(e,t={}){if("string"!=typeof e)return e;if('"'===e[0]&&'"'===e[e.length-1]&&-1===e.indexOf("\\"))return e.slice(1,-1);const r=e.trim();if(r.length<=9)switch(r.toLowerCase()){case"true":return!0;case"false":return!1;case"undefined":return;case"null":return null;case"nan":return Number.NaN;case"infinity":return Number.POSITIVE_INFINITY;case"-infinity":return Number.NEGATIVE_INFINITY}if(!T.test(e)){if(t.strict)throw new SyntaxError("[destr] Invalid JSON");return e}try{if(y.test(e)||_.test(e)){if(t.strict)throw new Error("[destr] Possible prototype pollution");return JSON.parse(e,w)}return JSON.parse(e)}catch(r){if(t.strict)throw r;return e}}const S=/#/g,I=/&/g,b=/\//g,C=/=/g,R=/\+/g,N=/%5e/gi,x=/%60/gi,M=/%7c/gi,U=/%20/gi;function k(e){return(t="string"==typeof e?e:JSON.stringify(e),encodeURI(""+t).replace(M,"|")).replace(R,"%2B").replace(U,"+").replace(S,"%23").replace(I,"%26").replace(x,"`").replace(N,"^").replace(b,"%2F");var t}function D(e){return k(e).replace(C,"%3D")}function P(e=""){try{return decodeURIComponent(""+e)}catch{return""+e}}function O(e){return P(e.replace(R," "))}function L(e){return P(e.replace(R," "))}function $(e=""){const t=Object.create(null);"?"===e[0]&&(e=e.slice(1));for(const r of e.split("&")){const e=r.match(/([^=]+)=?(.*)/)||[];if(e.length<2)continue;const a=O(e[1]);if("__proto__"===a||"constructor"===a)continue;const o=L(e[2]||"");void 0===t[a]?t[a]=o:Array.isArray(t[a])?t[a].push(o):t[a]=[t[a],o]}return t}function V(e){return Object.keys(e).filter(t=>void 0!==e[t]).map(t=>{return r=t,"number"!=typeof(a=e[t])&&"boolean"!=typeof a||(a=String(a)),a?Array.isArray(a)?a.map(e=>`${D(r)}=${k(e)}`).join("&"):`${D(r)}=${k(a)}`:D(r);var r,a}).filter(Boolean).join("&")}const F=/^[\s\w\0+.-]{2,}:([/\\]{1,2})/,W=/^[\s\w\0+.-]{2,}:([/\\]{2})?/,B=/^([/\\]\s*){2,}[^/\\]/,z=/^\.?\//;function q(e,t={}){return"boolean"==typeof t&&(t={acceptRelative:t}),t.strict?F.test(e):W.test(e)||!!t.acceptRelative&&B.test(e)}function j(e="",t){return e.endsWith("/")?e:e+"/"}function H(e,t){if(!(r=t)||"/"===r||q(e))return e;var r;const a=function(e=""){return(function(e=""){return e.endsWith("/")}(e)?e.slice(0,-1):e)||"/"}(t);return e.startsWith(a)?e:function(e,...t){let r=e||"";for(const e of t.filter(e=>function(e){return e&&"/"!==e}(e)))if(r){const t=e.replace(z,"");r=j(r)+t}else r=e;return r}(a,e)}function G(e,t){const r=function(e=""){const t=e.match(/^[\s\0]*(blob:|data:|javascript:|vbscript:)(.*)/i);if(t){const[,e,r=""]=t;return{protocol:e.toLowerCase(),pathname:r,href:e+r,auth:"",host:"",search:"",hash:""}}if(!q(e,{acceptRelative:!0}))return K(e);const[,r="",a,o=""]=e.replace(/\\/g,"/").match(/^[\s\0]*([\w+.-]{2,}:)?\/\/([^/@]+@)?(.*)/)||[];let[,s="",n=""]=o.match(/([^#/?]*)(.*)?/)||[];"file:"===r&&(n=n.replace(/\/(?=[A-Za-z]:)/,""));const{pathname:i,search:d,hash:l}=K(n);return{protocol:r.toLowerCase(),auth:a?a.slice(0,Math.max(0,a.length-1)):"",host:s,pathname:i,search:d,hash:l,[J]:!r}}(e),a={...$(r.search),...t};return r.search=V(a),function(e){const t=e.pathname||"",r=e.search?(e.search.startsWith("?")?"":"?")+e.search:"",a=e.hash||"",o=e.auth?e.auth+"@":"",s=e.host||"",n=e.protocol||e[J]?(e.protocol||"")+"//":"";return n+o+s+t+r+a}(r)}const J=Symbol.for("ufo:protocolRelative");function K(e=""){const[t="",r="",a=""]=(e.match(/([^#?]*)(\?[^#]*)?(#.*)?/)||[]).splice(1);return{pathname:t,search:r,hash:a}}class Q extends Error{constructor(e,t){super(e,t),this.name="FetchError",t?.cause&&!this.cause&&(this.cause=t.cause)}}const Z=new Set(Object.freeze(["PATCH","POST","PUT","DELETE"]));function X(e="GET"){return Z.has(e.toUpperCase())}const Y=new Set(["image/svg","application/xml","application/xhtml","application/html"]),ee=/^application\/(?:[\w!#$%&*.^`~-]*\+)?json(;.+)?$/i;function te(e,t,r,a){const o=function(e,t,r){if(!t)return new r(e);const a=new r(t);if(e)for(const[t,o]of Symbol.iterator in e||Array.isArray(e)?e:new r(e))a.set(t,o);return a}(t?.headers??e?.headers,r?.headers,a);let s;return(r?.query||r?.params||t?.params||t?.query)&&(s={...r?.params,...r?.query,...t?.params,...t?.query}),{...r,...t,query:s,params:s,headers:o}}async function re(e,t){if(t)if(Array.isArray(t))for(const r of t)await r(e);else await t(e)}const ae=new Set([408,409,425,429,500,502,503,504]),oe=new Set([101,204,205,304]);const se=function(){if("undefined"!=typeof globalThis)return globalThis;if("undefined"!=typeof self)return self;if("undefined"!=typeof window)return window;if("undefined"!=typeof global)return global;throw new Error("unable to locate global object")}(),ne=function e(t={}){const{fetch:r=globalThis.fetch,Headers:a=globalThis.Headers,AbortController:o=globalThis.AbortController}=t;async function s(e){const t=e.error&&"AbortError"===e.error.name&&!e.options.timeout||!1;if(!1!==e.options.retry&&!t){let t;t="number"==typeof e.options.retry?e.options.retry:X(e.options.method)?0:1;const r=e.response&&e.response.status||500;if(t>0&&(Array.isArray(e.options.retryStatusCodes)?e.options.retryStatusCodes.includes(r):ae.has(r))){const r="function"==typeof e.options.retryDelay?e.options.retryDelay(e):e.options.retryDelay||0;return r>0&&await new Promise(e=>setTimeout(e,r)),n(e.request,{...e.options,retry:t-1})}}const r=function(e){const t=e.error?.message||e.error?.toString()||"",r=e.request?.method||e.options?.method||"GET",a=e.request?.url||String(e.request)||"/",o=`[${r}] ${JSON.stringify(a)}`,s=e.response?`${e.response.status} ${e.response.statusText}`:"<no response>",n=new Q(`${o}: ${s}${t?` ${t}`:""}`,e.error?{cause:e.error}:void 0);for(const t of["request","options","response"])Object.defineProperty(n,t,{get:()=>e[t]});for(const[t,r]of[["data","_data"],["status","status"],["statusCode","status"],["statusText","statusText"],["statusMessage","statusText"]])Object.defineProperty(n,t,{get:()=>e.response&&e.response[r]});return n}(e);throw Error.captureStackTrace&&Error.captureStackTrace(r,n),r}const n=async function(e,n={}){const i={request:e,options:te(e,n,t.defaults,a),response:void 0,error:void 0};let d;if(i.options.method&&(i.options.method=i.options.method.toUpperCase()),i.options.onRequest&&await re(i,i.options.onRequest),"string"==typeof i.request&&(i.options.baseURL&&(i.request=H(i.request,i.options.baseURL)),i.options.query&&(i.request=G(i.request,i.options.query),delete i.options.query),"query"in i.options&&delete i.options.query,"params"in i.options&&delete i.options.params),i.options.body&&X(i.options.method)&&(!function(e){if(void 0===e)return!1;const t=typeof e;return"string"===t||"number"===t||"boolean"===t||null===t||"object"===t&&(!!Array.isArray(e)||!e.buffer&&(e.constructor&&"Object"===e.constructor.name||"function"==typeof e.toJSON))}(i.options.body)?("pipeTo"in i.options.body&&"function"==typeof i.options.body.pipeTo||"function"==typeof i.options.body.pipe)&&("duplex"in i.options||(i.options.duplex="half")):(i.options.body="string"==typeof i.options.body?i.options.body:JSON.stringify(i.options.body),i.options.headers=new a(i.options.headers||{}),i.options.headers.has("content-type")||i.options.headers.set("content-type","application/json"),i.options.headers.has("accept")||i.options.headers.set("accept","application/json"))),!i.options.signal&&i.options.timeout){const e=new o;d=setTimeout(()=>{const t=new Error("[TimeoutError]: The operation was aborted due to timeout");t.name="TimeoutError",t.code=23,e.abort(t)},i.options.timeout),i.options.signal=e.signal}try{i.response=await r(i.request,i.options)}catch(e){return i.error=e,i.options.onRequestError&&await re(i,i.options.onRequestError),await s(i)}finally{d&&clearTimeout(d)}if((i.response.body||i.response._bodyInit)&&!oe.has(i.response.status)&&"HEAD"!==i.options.method){const e=(i.options.parseResponse?"json":i.options.responseType)||function(e=""){if(!e)return"json";const t=e.split(";").shift()||"";return ee.test(t)?"json":Y.has(t)||t.startsWith("text/")?"text":"blob"}(i.response.headers.get("content-type")||"");switch(e){case"json":{const e=await i.response.text(),t=i.options.parseResponse||v;i.response._data=t(e);break}case"stream":i.response._data=i.response.body||i.response._bodyInit;break;default:i.response._data=await i.response[e]()}}return i.options.onResponse&&await re(i,i.options.onResponse),!i.options.ignoreResponseError&&i.response.status>=400&&i.response.status<600?(i.options.onResponseError&&await re(i,i.options.onResponseError),await s(i)):i.response},i=async function(e,t){return(await n(e,t))._data};return i.raw=n,i.native=(...e)=>r(...e),i.create=(r={},a={})=>e({...t,...a,defaults:{...t.defaults,...a.defaults,...r}}),i}({fetch:se.fetch?(...e)=>se.fetch(...e):()=>Promise.reject(new Error("[ofetch] global.fetch is not supported!")),Headers:se.Headers,AbortController:se.AbortController});function ie(e,t,r){return r}function de(){return Math.floor(9e5*Math.random())+1e5}function le(e,t){const r=new RegExp(`"${e}":"([^"]+)"`).exec(t);return r?.[1]}function ce(e){return e.replace(/\\<([^>]+?)\\>/g,(e,t)=>`<${t.replace(/\\+_/g,"_")}>`)}class he extends g{constructor(){super(),this.models={"gemini-1.5-flash":{"x-goog-ext-525001261-jspb":'[null,null,null,null,"418ab5ea040b5c43"]'},"gemini-1.5-pro":{"x-goog-ext-525001261-jspb":'[null,null,null,null,"9d60dfae93c9ff1f"]'},"gemini-1.5-pro-research":{"x-goog-ext-525001261-jspb":'[null,null,null,null,"e5a44cb1dae2b489"]'},"gemini-2.0-flash":{"x-goog-ext-525001261-jspb":'[null,null,null,null,"f299729663a2343f"]'},"gemini-2.0-flash-thinking":{"x-goog-ext-525001261-jspb":'[null,null,null,null,"9c17b1863f581b8a"]'},"gemini-2.0-flash-thinking-with-apps":{"x-goog-ext-525001261-jspb":'[null,null,null,null,"f8f8f5ea629f5d37"]'},"gemini-2.0-exp-advanced":{"x-goog-ext-525001261-jspb":'[null,null,null,null,"b1e46a6037e6aa9f"]'},"gemini-2.5-flash-exp":{"x-goog-ext-525001261-jspb":'[null,null,null,null,"35609594dbe934d8"]'},"gemini-2.5-pro-exp":{"x-goog-ext-525001261-jspb":'[null,null,null,null,"2525e3954d185b3c"]'},"gemini-deepresearch":{"x-goog-ext-525001261-jspb":'[null,null,null,null,"cd472a54d2abba7e"]'}},this.defaultModel="gemini-2.0-flash",this.initializeStorage().catch(console.error)}async initializeStorage(){(await this.getAllThreads()).length||await this.saveThreadsToStorage([]),await this.validateExistingThreads()}async validateExistingThreads(){const e=await this.getAllThreads();let t=!1;for(const r of e)r.modelName!==this.getName()||this.isValidGeminiMetadata(r.metadata)||(await this.deleteThread(r.id),t=!0);t&&await this.saveThreadsToStorage(e.filter(e=>e.modelName!==this.getName()||this.isValidGeminiMetadata(e.metadata)))}isValidGeminiMetadata(e){return void 0!==e?.conversationId&&e?.contextIds&&Array.isArray(e.contextIds)&&3===e.contextIds.length&&e?.requestParams?.atValue&&e.requestParams?.blValue&&e.requestParams?.sid&&"string"==typeof e?.emoji&&"string"==typeof e?.defaultLang&&e.defaultLang&&"string"==typeof e?.defaultModel&&e.defaultModel&&"string"==typeof e?.shareUrl}getGeminiMetadata(){const t=this.getCurrentThreadSafe();if(!t.metadata)return this.handleModelError("No thread metadata available",e.ErrorCode.INVALID_REQUEST);const r=t.metadata;return this.isValidGeminiMetadata(r)?r:this.handleModelError("Invalid thread metadata",e.ErrorCode.INVALID_REQUEST)}getName(){return"Google Bard"}supportsImageInput(){return!0}async initNewThread(){this.currentThread={id:u(),title:"New Conversation",messages:[],createdAt:Date.now(),updatedAt:Date.now(),modelName:this.getName(),metadata:{conversationId:"",contextIds:["","",""],requestParams:await this.fetchRequestParams(),emoji:"",defaultLang:"en",defaultModel:"2.0 Flash",shareUrl:""}},await this.saveThread()}async fetchRequestParams(){try{const t=await ne("https://gemini.google.com/",{responseType:"text"}),r=le("SNlM0e",t),a=le("cfb2h",t),o=le("FdrFJe",t);return r&&a&&o?{atValue:r,blValue:a,sid:o}:this.handleModelError("Failed to extract Bard parameters",e.ErrorCode.UNAUTHORIZED)}catch(t){return this.handleModelError("Failed to initialize Bard session",e.ErrorCode.UNAUTHORIZED,void 0,t)}}parseBardResponse(t){try{const r=t.split("\n").find(e=>e.includes('"rc_'));if(!r)return this.handleModelError("Could not find primary data line in response text for fallback parsing.",e.ErrorCode.RESPONSE_PARSING_ERROR);let a;try{a=JSON.parse(r)}catch(t){return this.handleModelError(`Failed to parse data line JSON: ${t}`,e.ErrorCode.RESPONSE_PARSING_ERROR,void 0,t)}if(!Array.isArray(a)||!a[0]||!a[0][2])return this.handleModelError("Unexpected structure in parsed data line.",e.ErrorCode.RESPONSE_PARSING_ERROR);const o=a[0][2],s=JSON.parse(o);if(!s)return this.handleModelError("Empty response data",e.ErrorCode.RESPONSE_PARSING_ERROR);const n=s[4]?.[0]?.[1]?.[0]??"",i=[s[1]?.[0]??"",s[1]?.[1]??"",s[4]?.[0]?.[0]??""],d=s[4]?.[0]?.[4]||[];let l=n;for(const e of d){const[t,r,a]=e;t&&r&&a&&t[0]?.[0]&&r[0]?.[0]&&t[4]&&(l=l.replace(a,`[![${t[4]}](${t[0][0]})](${r[0][0]})`))}return{text:l,ids:i}}catch(t){return this.handleModelError(`Failed to parse Bard response (fallback): ${t}`,e.ErrorCode.RESPONSE_PARSING_ERROR,void 0,t)}}async uploadImage(t){try{const r={"content-type":"application/x-www-form-urlencoded;charset=UTF-8","push-id":"feeds/mcudyrk2a4khkz","x-goog-upload-header-content-length":t.size.toString(),"x-goog-upload-protocol":"resumable","x-tenant-id":"bard-storage"},a=(await ne.raw("https://content-push.googleapis.com/upload/",{method:"POST",headers:{...r,"x-goog-upload-command":"start"},body:new URLSearchParams({[`File name: ${t.name}`]:""})})).headers.get("x-goog-upload-url");if(!a)return this.handleModelError("Failed to get upload URL for image",e.ErrorCode.SERVICE_UNAVAILABLE);return await ne(a,{method:"POST",headers:{...r,"x-goog-upload-command":"upload, finalize","x-goog-upload-offset":"0"},body:t})}catch(r){return this.handleModelError(`Failed to finalize image upload: ${t.name}`,e.ErrorCode.UPLOAD_FAILED,void 0,r)}}async ensureThreadLoaded(){if(!this.currentThread){const e=(await this.getAllThreads()).filter(e=>e.modelName===this.getName()&&this.isValidGeminiMetadata(e.metadata));if(e.length>0){const t=e.sort((e,t)=>t.updatedAt-e.updatedAt)[0];this.currentThread=t,console.log("Loaded existing thread from storage:",this.currentThread.id)}else await this.initNewThread()}}async doSendMessage(t){let a;t.images&&t.images.length>1&&this.handleModelError("Gemini Web only supports one image per message.",e.ErrorCode.UPLOAD_AMOUNT_EXCEEDED,t);try{t.onEvent({type:"UPDATE_ANSWER",data:{text:""}}),await this.ensureThreadLoaded(),a=this.getCurrentThreadSafe();const r=this.createMessage("user",t.prompt);a.messages.push(r);const o=this.getBardMetadata();let s,n;if(console.log("Current context IDs before request:",o.contextIds),t.images&&t.images.length>0){t.images.length>1&&console.warn("GeminiWebModel only supports one image per message. Using the first image."),n=t.images[0];try{s=await this.uploadImage(n),r.metadata={...r.metadata||{},attachmentUrl:s}}catch(r){this.handleModelError(`Failed to upload image: ${n.name}`,e.ErrorCode.UPLOAD_FAILED,t,r)}}const i=[null,JSON.stringify([[t.prompt,0,null,s&&n?[[[s,1],n.name]]:[]],null,o.contextIds])],d=this.models[t.model||this.defaultModel],l=await fetch("https://gemini.google.com/_/BardChatUi/data/assistant.lamda.BardFrontendService/StreamGenerate",{method:"POST",signal:t.signal,headers:{"Content-Type":"application/x-www-form-urlencoded;charset=UTF-8",...d||{}},body:new URLSearchParams({at:o.requestParams.atValue,"f.req":JSON.stringify(i),bl:o.requestParams.blValue,_reqid:String(de()),rt:"c"})});if(!l.ok||!l.body){const r=await l.text();return this.handleModelError(`Gemini API error: ${l.status} - ${r.substring(0,200)}`,e.ErrorCode.SERVICE_UNAVAILABLE,t)}const c=l.body.getReader(),h=new TextDecoder;let u="",g="",m=null,p=!1;for(;;){const{done:e,value:r}=await c.read();if(e)break;let o;for(u+=h.decode(r,{stream:!0});(o=u.indexOf("\n"))>=0;){const e=u.slice(0,o).trim();if(u=u.slice(o+1),e&&!/^\d+$/.test(e)&&")]}'"!==e)try{const r=JSON.parse(e);if(Array.isArray(r)&&r.length>0&&Array.isArray(r[0])&&"wrb.fr"===r[0][0]){const e=r[0][2];if("string"==typeof e){const r=JSON.parse(e);if(!p&&a.messages.length<=1&&r&&r[10]&&Array.isArray(r[10])&&"string"==typeof r[10][0]){let e=r[10][0];e.endsWith("\n")&&(e=e.slice(0,-1)),e&&(t.onEvent({type:"TITLE_UPDATE",data:{title:e,threadId:a.id}}),a.title=e,p=!0)}if(r&&r[4]?.[0]?.[1]?.[0]){let e=r[4][0][1][0];console.log(e),e=ce(e),console.log(e),e!==g&&(g=e,t.onEvent({type:"UPDATE_ANSWER",data:{text:g}}))}if(r&&r[1]?.[0]&&r[1]?.[1]&&r[4]?.[0]?.[0]&&(m=[r[1][0],r[1][1],r[4][0][0]],a.metadata)){const e=a.metadata;JSON.stringify(e.contextIds)!==JSON.stringify(m)&&(e.contextIds=m,console.log("Updated context IDs mid-stream:",m)),m[0]&&e.conversationId!==m[0]&&(e.conversationId=m[0],console.log("Updated conversationId mid-stream:",m[0]))}}}}catch(t){console.warn("Error parsing Gemini stream line:",e,t)}}}if(!m){console.warn("Final IDs not found in stream, attempting fallback parse.");try{m=this.parseBardResponse(g).ids}catch(r){return console.error("Fallback parsing failed:",r),this.handleModelError("Failed to extract final IDs from response stream",e.ErrorCode.RESPONSE_PARSING_ERROR,t,r)}}const E=this.createMessage("assistant",g);if(E.metadata={messageId:m[1]},a.messages.push(E),a.metadata){const e=a.metadata;e.contextIds=m,e.conversationId=m[0]||""}a.updatedAt=Date.now(),await this.saveThread(),t.onEvent({type:"DONE",data:{threadId:a.id}})}catch(a){this.handleModelError("Error during Gemini message sending or processing",a instanceof r?a.code:e.ErrorCode.NETWORK_ERROR,t,a)}}async loadThread(e){const t=await this.getAllThreads(),r=t.find(t=>t.id===e);if(r&&r.modelName===this.getName()){this.currentThread=r;this.currentThread.metadata.requestParams=await this.fetchRequestParams(),await this.saveThread(),await this.saveThreadsToStorage(t)}}getBardMetadata(){const t=this.getCurrentThreadSafe();if(!t.metadata)return this.handleModelError("No thread metadata available",e.ErrorCode.INVALID_REQUEST);if(!this.isValidGeminiMetadata(t.metadata))return this.handleModelError("Invalid or incomplete thread metadata",e.ErrorCode.INVALID_REQUEST);return t.metadata}getCurrentThreadSafe(){return this.currentThread?this.currentThread:this.handleModelError("No active thread",e.ErrorCode.INVALID_REQUEST)}async saveThread(){if(!this.currentThread)return;const e=await this.getAllThreads(),t=e.findIndex(e=>e.id===this.currentThread.id);-1!==t?e[t]=this.currentThread:e.push(this.currentThread),await this.saveThreadsToStorage(e)}async editTitle(t,r,a){try{let o,s=!1!==a?.loadThread,n=!1!==a?.tryUpdateThread;if(console.log(s,n),s)await this.ensureThreadLoaded(),o=this.getBardMetadata();else{if(!a?.metadata)return this.handleModelError("No thread loaded and no metadata provided for title edit",e.ErrorCode.INVALID_REQUEST);if(o=a.metadata,!this.isValidGeminiMetadata(o))return this.handleModelError("Invalid metadata provided for title edit",e.ErrorCode.INVALID_REQUEST)}const i=o.conversationId;if(!i){const t=o.contextIds[0];if(!t)return this.handleModelError("Missing conversation ID in metadata for editTitle",e.ErrorCode.INVALID_REQUEST);console.warn("Using fallback conversation ID from contextIds[0] for editTitle"),o.conversationId=t,await this.saveThread()}const d=[i,t];r&&d.push(null,null,r,null,null,null,null,null,[1,r]);const l=JSON.stringify([null,[["title","icon","user_selected_icon"]],d]),c=JSON.stringify([["MUAZcd",l,null,"generic"]]),h=new URL("https://gemini.google.com/_/BardChatUi/data/batchexecute");h.searchParams.set("rpcids","MUAZcd"),h.searchParams.set("source-path",`/app/${i}`),h.searchParams.set("bl",o.requestParams.blValue);const u=o.requestParams.sid??String(-Math.floor(9e18*Math.random()));h.searchParams.set("f.sid",u),h.searchParams.set("hl","en"),h.searchParams.set("_reqid",String(de())),h.searchParams.set("rt","c");let g=new URLSearchParams({at:o.requestParams.atValue,"f.req":"["+c+"]"});const m=await ne.raw(h.toString(),{method:"POST",headers:{"Content-Type":"application/x-www-form-urlencoded;charset=UTF-8"},body:g.toString()+"&",parseResponse:t=>{const r=t.substring(t.indexOf("\n")+1).split("\n").find(e=>e.trim().startsWith('[["wrb.fr"'));return r?JSON.parse(r):(console.error("Raw editTitle response:",t),this.handleModelError("Could not find data line in editTitle response",e.ErrorCode.RESPONSE_PARSING_ERROR))}});if(console.log("Raw editTitle response:",m),console.log("Parsed e response:",m?._data[0][0]),console.log("Parersed e response:",m?._data[0][1]),!m||"object"!=typeof m||"wrb.fr"!==m?._data[0][0]||"MUAZcd"!==m?._data[0][1]||!m?._data[0][2])return this.handleModelError("Title update failed. Server response did not indicate success.",e.ErrorCode.UNKNOWN_ERROR);console.log("Title updated successfully on server.");try{const e=JSON.parse(m?._data[0][2]),t=e[1][1],r=e[1][4];console.log("Server response after title update:",e),console.log("Server title change confirmation received:",t),console.log(this.currentThread),n&&this.currentThread&&this.currentThread.metadata&&(console.log(this.currentThread),this.currentThread.title=t,this.currentThread.metadata.emoji=r||"",await this.saveThread(),console.log("Thread updated locally after title change confirmation."))}catch(t){return console.warn("Could not parse success response details:",t),console.error("Unexpected response structure after title update. Could not parse response:",m),this.handleModelError("Title update succeeded but response format unexpected",e.ErrorCode.RESPONSE_PARSING_ERROR,void 0,t)}}catch(t){this.handleModelError("Error updating conversation title",e.ErrorCode.SERVICE_UNAVAILABLE,void 0,t)}}async deleteServerThreads(t,r=!0,a=!0){try{const o=await this.getAllThreads();for(const s of t)try{const t=o.find(e=>e.metadata?.conversationId===s);if(!t){console.warn(`[deleteServerThreads] Thread ${s} not found locally.`);continue}if(t.modelName!==this.getName()){console.warn(`[deleteServerThreads] Thread ${s} has incorrect model name: ${t.modelName}. Skipping.`);continue}if(!this.isValidGeminiMetadata(t.metadata)){console.warn(`[deleteServerThreads] Thread ${s} has invalid or missing metadata. Cannot delete from server.`),r&&(console.warn(`[deleteServerThreads] Deleting thread ${s} locally due to invalid metadata.`),await this.deleteThread(t.id,a));continue}const n=t.metadata;let i=n.conversationId,d=n.requestParams.atValue,l=n.requestParams.blValue;if(!i){const e=n.contextIds[0];if(!e){console.warn(`[deleteServerThreads] Missing conversation ID in metadata for thread ${s}. Cannot delete from server.`),r&&(console.warn(`[deleteServerThreads] Deleting thread ${s} locally due to missing conversation ID.`),await this.deleteThread(t.id,a));continue}console.warn(`[deleteServerThreads] Using fallback conversation ID from contextIds[0] for thread ${s}.`),n.conversationId=e,i=e,await this.saveThread()}if(!d||!l){console.warn(`[deleteServerThreads] Missing 'at' or 'bl' value in requestParams for thread ${s}. Fetching fresh params.`);try{n.requestParams=await this.fetchRequestParams(),d=n.requestParams.atValue,l=n.requestParams.blValue,await this.saveThread()}catch(e){console.error(`[deleteServerThreads] Failed to refresh requestParams for thread ${s}. Skipping server delete.`,e),r&&(console.warn(`[deleteServerThreads] Deleting thread ${s} locally due to failed param refresh.`),await this.deleteThread(t.id,a));continue}}const c="https://gemini.google.com/_/BardChatUi/data/batchexecute",h=JSON.stringify([["GzXR5e",JSON.stringify([i]),null,"generic"]]),u=new URLSearchParams({rpcids:"GzXR5e","source-path":"/app",bl:l,"f.sid":n.requestParams.sid??String(-Math.floor(9e18*Math.random())),hl:"en",_reqid:String(de()),rt:"c"}),g=new URLSearchParams({at:d,"f.req":`[${h}]`});console.log(`[deleteServerThreads] Sending Request 1 for ${s} (ConvID: ${i})`);const m=await ne.raw(`${c}?${u.toString()}`,{method:"POST",headers:{"Content-Type":"application/x-www-form-urlencoded;charset=UTF-8"},body:g.toString()+"&",parseResponse:e=>e});if(200!==m.status)return console.error(`[deleteServerThreads] Request 1 failed for ${s}. Status: ${m.status}`,await m._data),this.handleModelError(`Request 1 failed with status ${m.status}`,e.ErrorCode.SERVICE_UNAVAILABLE);console.log(`[deleteServerThreads] Request 1 successful for ${s}.`);const p=JSON.stringify([["qWymEb",JSON.stringify([i,[1,null,0,1]]),null,"generic"]]),E=new URLSearchParams({rpcids:"qWymEb","source-path":"/app",bl:l,"f.sid":n.requestParams.sid??String(-Math.floor(9e18*Math.random())),hl:"en",_reqid:String(de()),rt:"c"}),f=new URLSearchParams({at:d,"f.req":`[${p}]`});console.log(`[deleteServerThreads] Sending Request 2 for ${s} (ConvID: ${i})`);const A=await ne.raw(`${c}?${E.toString()}`,{method:"POST",headers:{"Content-Type":"application/x-www-form-urlencoded;charset=UTF-8"},body:f.toString()+"&",parseResponse:e=>e});if(200!==A.status)return console.error(`[deleteServerThreads] Request 2 failed for ${s}. Status: ${A.status}`,await A._data),this.handleModelError(`Request 2 failed with status ${A.status}`,e.ErrorCode.SERVICE_UNAVAILABLE);console.log(`[deleteServerThreads] Request 2 successful for ${s}.`),r&&(console.log(`[deleteServerThreads] Deleting thread ${s} locally.`),await this.deleteThread(t.id,a))}catch(e){console.error(`[deleteServerThreads] Failed to process thread ${s}:`,e)}}catch(t){return this.handleModelError("Error during server thread deletion process",e.ErrorCode.SERVICE_UNAVAILABLE,void 0,t)}}async getConversationData(t){try{let r,a=!1!==t?.loadThread;if(t?.metadata&&(a=!1),a)await this.ensureThreadLoaded(),r=this.getBardMetadata();else{if(!t?.metadata)return this.handleModelError("No thread loaded and no metadata provided for getting conversation data",e.ErrorCode.INVALID_REQUEST);if(r=t.metadata,!this.isValidGeminiMetadata(r))return this.handleModelError("Invalid metadata provided for getting conversation data",e.ErrorCode.INVALID_REQUEST)}const o=r.conversationId||r.contextIds[0];if(!o)return this.handleModelError("Missing conversation ID in metadata",e.ErrorCode.INVALID_REQUEST);const s=JSON.stringify([o,10,null,1,[1],null,null,1]),n=JSON.stringify([["hNvQHb",s,null,"generic"]]),i=new URL("https://gemini.google.com/_/BardChatUi/data/batchexecute");i.searchParams.set("rpcids","hNvQHb"),i.searchParams.set("source-path",`/app/${o.substring(2)}`),i.searchParams.set("bl",r.requestParams.blValue);const d=r.requestParams.sid??String(-Math.floor(9e18*Math.random()));i.searchParams.set("f.sid",d),i.searchParams.set("hl","en"),i.searchParams.set("_reqid",String(de())),i.searchParams.set("rt","c");let l=new URLSearchParams({"f.req":`[${n}]`,at:r.requestParams.atValue});const c=await ne(i.toString(),{method:"POST",headers:{"Content-Type":"application/x-www-form-urlencoded;charset=UTF-8"},body:l+"&",parseResponse:t=>{const r=t.substring(t.indexOf("\n")+1).split("\n").find(e=>e.trim().startsWith('[["wrb.fr"'));return r?JSON.parse(r):(console.error("Raw getConversationData response:",t),this.handleModelError("Could not find data line in getConversationData response",e.ErrorCode.RESPONSE_PARSING_ERROR))}});if(console.log(c),!c||!Array.isArray(c)||0===c.length||!Array.isArray(c[0])||c[0].length<3)return console.error("Unexpected response structure in getConversationData:",c),this.handleModelError("Unexpected response structure in getConversationData",e.ErrorCode.RESPONSE_PARSING_ERROR);const h=c[0][2],u=JSON.parse(h),g=[];if(Array.isArray(u?.[0]))for(const e of u[0].slice().reverse()){if(!Array.isArray(e)||e.length<4){console.warn("Skipping invalid message pair structure:",e);continue}const t=e[2],r=e[4];let a=null;if(Array.isArray(t)&&Array.isArray(t[0])&&"string"==typeof t[0][0]){const e=t[0][0],o=Array.isArray(r)&&"number"==typeof r[0]?r[0]:void 0;a=this.createMessage("user",e),void 0!==o&&(a.timestamp=o),a.metadata={}}else console.warn("Could not extract user message text from:",t);const o=e[3],s=e[4];let n=null;if(Array.isArray(o)&&Array.isArray(o[0])&&Array.isArray(o[0][0])){const e=o[0][0];if(Array.isArray(e)&&e.length>1&&Array.isArray(e[1])&&"string"==typeof e[1][0]){const t=e[1][0],r=Array.isArray(s)&&"number"==typeof s[0]?s[0]:void 0;n=this.createMessage("assistant",t),void 0!==r&&(n.timestamp=r),n.metadata={}}else console.warn("Could not extract assistant message text from:",e)}else console.warn("Could not extract assistant message wrapper from:",o);a&&g.push(a),n&&g.push(n)}else console.warn("No message pairs found in conversation payload:",u);return g}catch(t){return console.error("Error getting conversation data:",t),this.handleModelError(`Failed to get conversation data: ${t instanceof Error?t.message:String(t)}`,t instanceof r?t.code:e.ErrorCode.SERVICE_UNAVAILABLE,void 0,t)}}async shareConversation(t){try{let r=!1!==t?.loadThread;t?.metadata&&(r=!1),t||(t={});let a,o=t?.title||"",s=t?.modelName||"",n=t?.language||"";if(r)await this.ensureThreadLoaded(),a=this.getGeminiMetadata(),o||(o=this.currentThread?.title||""),s||(s=this.currentThread?.metadata?.defaultModel||""),n||(n=this.currentThread?.metadata?.defaultLang||"");else{if(!t?.metadata)return this.handleModelError("No thread loaded and no metadata provided for sharing",e.ErrorCode.INVALID_REQUEST);if(!this.isValidGeminiMetadata(t?.metadata))return this.handleModelError("Invalid metadata provided for sharing",e.ErrorCode.INVALID_REQUEST);a=t.metadata,s=a.defaultModel||"",n=a.defaultLang||""}o||(console.warn('Title is required when sharing a conversation, but not provided or is blank. Using "Untitled Conversation" as title.'),o="Untitled Conversation"),s||(console.warn('Model name is required when sharing a conversation, but not provided. Using default model name "2.0 Flash".'),s="2.0 Flash"),n||(console.warn('Language is required when sharing a conversation, but not provided. Using default language "en".'),n="en");const i=[[["fuVx7",JSON.stringify([null,a.contextIds[0],null,a.contextIds[2],[1,o+"\n",null,null,null,["","",""],null,[null,null,s]],[n],0]),null,"generic"]]],d=new URLSearchParams({"f.req":JSON.stringify(i),at:a.requestParams.atValue}),l=`https://gemini.google.com/_/BardChatUi/data/batchexecute?${new URLSearchParams({rpcids:"fuVx7","source-path":`/app/${a.contextIds[0].substring(2)}`,bl:a.requestParams.blValue,"f.sid":a.requestParams.sid,hl:t.language||"en",_reqid:String(de()),rt:"c"})}`,c=await ne(l,{method:"POST",headers:{"Content-Type":"application/x-www-form-urlencoded;charset=UTF-8"},body:d+"&",parseResponse:e=>e,onResponse:({response:t})=>{if(!t.ok)return this.handleModelError(`Failed to share conversation: ${t.status}`,e.ErrorCode.SERVICE_UNAVAILABLE,void 0,t.statusText)}}),h=c.split("\n").find(e=>e.includes('"wrb.fr"'));if(!h)return this.handleModelError("Failed to parse Gemini share response",e.ErrorCode.RESPONSE_PARSING_ERROR);console.log(h);let u="";try{const e=JSON.parse(h);if(console.log(e),e[0]&&Array.isArray(e[0])&&e[0][2]){const t=JSON.parse(e[0][2]);Array.isArray(t)&&t[2]&&(u=t[2])}}catch(t){return this.handleModelError("Error extracting share ID from Gemini response",e.ErrorCode.RESPONSE_PARSING_ERROR,void 0,t)}if(!u)return this.handleModelError("No share ID found in Gemini response",e.ErrorCode.RESPONSE_PARSING_ERROR);const g=`https://g.co/gemini/share/${u}`;return r&&this.currentThread&&this.currentThread.metadata&&(this.currentThread.metadata.shareUrl=g,await this.saveThread()),g}catch(t){return this.handleModelError("Error sharing conversation",e.ErrorCode.SERVICE_UNAVAILABLE,void 0,t)}}async unShareConversation(t){try{let r,a=!1,o=!1!==t?.loadThread,s=!1!==t?.updateThread;if(t?.metadata&&(o=!1),t||(t={}),o)await this.ensureThreadLoaded(),r=this.getGeminiMetadata();else{if(!t?.metadata)return this.handleModelError("No thread loaded and no metadata provided for sharing",e.ErrorCode.INVALID_REQUEST);if(s)return this.handleModelError("Cannot update thread when LoadThread option is false (updateThread option is not supported)",e.ErrorCode.INVALID_REQUEST);if(!this.isValidGeminiMetadata(t?.metadata))return this.handleModelError("Invalid metadata provided for sharing",e.ErrorCode.INVALID_REQUEST);r=t.metadata}if(!r.shareUrl||!r?.shareUrl.includes("https://g.co/gemini/share/"))return this.handleModelError("No share URL found in metadata",e.ErrorCode.INVALID_REQUEST);const n=[[["SgORbf",JSON.stringify([null,r.shareUrl.replace("https://g.co/gemini/share/","")]),null,"generic"]]],i=new URLSearchParams({"f.req":JSON.stringify(n),at:r.requestParams.atValue}),d=`https://gemini.google.com/_/BardChatUi/data/batchexecute?${new URLSearchParams({rpcids:"fuVx7","source-path":`/app/${r.shareUrl.replace("https://g.co/gemini/share/","")}`,bl:r.requestParams.blValue,"f.sid":r.requestParams.sid,hl:r.defaultLang||"en",_reqid:String(de()),rt:"c"})}`,l=await ne(d,{method:"POST",headers:{"Content-Type":"application/x-www-form-urlencoded;charset=UTF-8"},body:i+"&",parseResponse:e=>e,onResponse:({response:t})=>{if(!t.ok)return this.handleModelError(`Failed to un-share conversation: ${t.status}`,e.ErrorCode.SERVICE_UNAVAILABLE,void 0,t.statusText)}}),c=l.split("\n").find(e=>e.includes('"wrb.fr"'));if(!c)return this.handleModelError("Failed to parse Gemini un-share response",e.ErrorCode.RESPONSE_PARSING_ERROR);console.log(c);try{const e=JSON.parse(c);if(console.log(e),e[0]&&Array.isArray(e[0])&&e[0][2])return a="[]"===e[0][2],a&&s&&this.currentThread&&this.currentThread.metadata&&(this.currentThread.metadata.shareUrl="",await this.saveThread()),a}catch(t){return this.handleModelError("Failed to parse Gemini un-share response",e.ErrorCode.RESPONSE_PARSING_ERROR,void 0,t)}return a}catch(t){return this.handleModelError("Error sharing conversation",e.ErrorCode.SERVICE_UNAVAILABLE,void 0,t)}}}f([ie,A("design:type",Function),A("design:paramtypes",[File]),A("design:returntype",Promise)],he.prototype,"uploadImage",null),f([ie,A("design:type",Function),A("design:paramtypes",[String,String,Object]),A("design:returntype",Promise)],he.prototype,"editTitle",null),f([ie,A("design:type",Function),A("design:paramtypes",[Array,Boolean,Boolean]),A("design:returntype",Promise)],he.prototype,"deleteServerThreads",null),f([ie,A("design:type",Function),A("design:paramtypes",[Object]),A("design:returntype",Promise)],he.prototype,"getConversationData",null),f([ie,A("design:type",Function),A("design:paramtypes",[Object]),A("design:returntype",Promise)],he.prototype,"shareConversation",null),f([ie,A("design:type",Function),A("design:paramtypes",[Object]),A("design:returntype",Promise)],he.prototype,"unShareConversation",null);var ue=null;"undefined"!=typeof WebSocket?ue=WebSocket:"undefined"!=typeof MozWebSocket?ue=MozWebSocket:"undefined"!=typeof global?ue=global.WebSocket||global.MozWebSocket:"undefined"!=typeof window?ue=window.WebSocket||window.MozWebSocket:"undefined"!=typeof self&&(ue=self.WebSocket||self.MozWebSocket);var ge=ue;function me(e,t,r){return r}class pe extends g{constructor(e={}){super(),this.sessionKey=e.sessionKey,this.initializeStorage().catch(console.error)}async initializeStorage(){(await this.getAllThreads()).length||await this.saveThreadsToStorage([]),await this.validateExistingThreads()}async validateExistingThreads(){const e=await this.getAllThreads();let t=!1;for(const r of e)r.modelName!==this.getName()||this.isValidClaudeMetadata(r.metadata)||(await this.deleteThread(r.id),t=!0);t&&await this.saveThreadsToStorage(e.filter(e=>e.modelName!==this.getName()||this.isValidClaudeMetadata(e.metadata)))}isValidClaudeMetadata(e){return e?.organizationId&&e?.conversationId}getName(){return"Claude Web"}supportsImageInput(){return!0}async createConversation(t){const r=u();try{await ne(`https://claude.ai/api/organizations/${t}/chat_conversations`,{method:"POST",headers:this.getHeaders(),credentials:"include",body:{name:"",uuid:r}});return r}catch(t){return t instanceof Q&&403===t.status?this.handleModelError("There is no logged-in Claude account in this browser.",e.ErrorCode.UNAUTHORIZED,void 0,t):this.handleModelError("Failed to create conversation",e.ErrorCode.SERVICE_UNAVAILABLE,void 0,t)}}async generateChatTitle(e,t,r){try{const a=await ne(`https://claude.ai/api/organizations/${e}/chat_conversations/${t}/title`,{method:"POST",headers:this.getHeaders(),credentials:"include",body:{message_content:r,recent_titles:[]}});return a.title?a.title:"New Conversation"}catch(e){return console.error("Failed to generate chat title:",e),"New Conversation"}}getHeaders(){const e={"Content-Type":"application/json",Accept:"application/json"};return this.sessionKey&&(e.Cookie=`sessionKey=${this.sessionKey}`),e}async ensureThreadLoaded(){if(!this.currentThread){const e=(await this.getAllThreads()).filter(e=>e.modelName===this.getName()&&this.isValidClaudeMetadata(e.metadata));if(e.length>0){const t=e.sort((e,t)=>t.updatedAt-e.updatedAt)[0];this.currentThread=t,console.log("Loaded existing thread from storage:",this.currentThread.id)}else await this.initNewThread()}}getClaudeMetadata(){const t=this.getCurrentThreadSafe();if(!t.metadata)return this.handleModelError("No thread metadata available",e.ErrorCode.INVALID_REQUEST);const r=t.metadata;return r.organizationId&&r.conversationId?r:this.handleModelError("Invalid thread metadata",e.ErrorCode.INVALID_REQUEST)}getCurrentThreadSafe(){return this.currentThread?this.currentThread:this.handleModelError("No active thread",e.ErrorCode.INVALID_REQUEST)}async initNewThread(){try{const t=await this.getOrganizationId();if(!t)return this.handleModelError("Organization ID is required",e.ErrorCode.INVALID_REQUEST);const r=await this.createConversation(t);this.currentThread={id:r,title:"New Conversation",modelName:this.getName(),messages:[],createdAt:Date.now(),updatedAt:Date.now(),metadata:{organizationId:t,conversationId:r}},await this.saveThread()}catch(t){return this.handleModelError("Error initializing new thread",e.ErrorCode.METADATA_INITIALIZATION_ERROR,void 0,t)}}async loadThread(t){const r=(await this.getAllThreads()).find(e=>e.id===t);if(!r||r.modelName!==this.getName())return this.handleModelError(`Thread ${t} not found`,e.ErrorCode.INVALID_THREAD_ID);this.currentThread=r,!this.organizationId&&r.metadata&&(this.organizationId=r.metadata.organizationId)}async doSendMessage(t){t.images&&t.images.length>1&&this.handleModelError("Claude Web only supports one image per message.",e.ErrorCode.UPLOAD_AMOUNT_EXCEEDED,t);try{t.onEvent({type:"UPDATE_ANSWER",data:{text:""}}),await this.ensureThreadLoaded();const r=this.getCurrentThreadSafe(),a=this.getClaudeMetadata();let o;t.images&&t.images.length>0&&(t.images.length>1&&console.warn("ClaudeWebModel currently only supports one image per message. Using the first image."),o=t.images[0]);const s=this.createMessage("user",t.prompt);o&&(s.metadata={...s.metadata||{},imageDataUrl:await this.fileToDataUrl(o)}),r.messages.push(s);let n={},i=[];if(o){const r=new FormData;r.append("file",o);try{const o=await fetch(`https://claude.ai/api/${a.organizationId}/upload`,{method:"POST",credentials:"include",body:r});if(!o.ok){const r=await o.text();return this.handleModelError(`Failed to upload image: ${r}`,e.ErrorCode.UPLOAD_FAILED,t,r)}if(n=await o.json(),!n||!n.file_uuid)return this.handleModelError("Invalid upload response format",e.ErrorCode.UPLOAD_FAILED,t);i=[{file_name:n.file_name,file_size:n.file_size,file_type:n.file_type,file_uuid:n.file_uuid,source:"file_upload"}]}catch(r){this.handleModelError(`Failed to upload image: ${o.name}`,e.ErrorCode.UPLOAD_FAILED,t,r)}}let d=await this.getStyles(a.organizationId),l=this.findStyleByKey(d,t.style_key||"");t.style_key&&!l&&console.warn(`Style key '${t.style_key}' not found, using default style.`);const c=await fetch(`https://claude.ai/api/organizations/${a.organizationId}/chat_conversations/${a.conversationId}/completion`,{method:"POST",headers:this.getHeaders(),credentials:"include",signal:t.signal,body:JSON.stringify({attachments:i,files:[],locale:navigator.language||"en-US",personalized_styles:l?[l]:[],prompt:t.prompt,timezone:Intl.DateTimeFormat().resolvedOptions().timeZone})});if(!c.ok){const r=await c.text();try{const a=JSON.parse(r);if(429===c.status&&"error"===a.type&&"rate_limit_error"===a.error?.type)try{a.error.message=JSON.parse(a.error.message);let r="";if(a.error.resetsAt){const e=new Date(1e3*a.error.resetsAt);a.error.resetsAt.resetsAtReadable=e.toLocaleString(),r=` Rate limit resets at ${a.error.resetsAt.resetsAtReadable}`}return this.handleModelError(`Claude rate limit exceeded.${r}`,e.ErrorCode.RATE_LIMIT_EXCEEDED,t,a)}catch(r){return this.handleModelError(`Claude rate limit exceeded: ${a.error.message}`,e.ErrorCode.RATE_LIMIT_EXCEEDED,t,a)}return this.handleModelError(`Claude API error: ${JSON.stringify(a)}`,e.ErrorCode.SERVICE_UNAVAILABLE,t)}catch(a){return 429===c.status?this.handleModelError("Claude rate limit exceeded. Please try again later.",e.ErrorCode.RATE_LIMIT_EXCEEDED,t):this.handleModelError(`Claude API error: ${c.status} - ${r.substring(0,200)}`,e.ErrorCode.SERVICE_UNAVAILABLE,t)}}if(!c.body)return this.handleModelError("Response body is null",e.ErrorCode.SERVICE_UNAVAILABLE,t);const h=c.body.getReader(),u=new TextDecoder;let g="",m="",p="",E="";for(;;){const{done:e,value:r}=await h.read();if(e)break;g+=u.decode(r,{stream:!0});const a=g.split("\n");g=a.pop()||"";for(const e of a)if(e.trim())e.startsWith("event:")?p=e.substring(6).trim():e.startsWith("data:")&&(E=e.substring(5).trim());else if(p&&E){const e=this.processEvent(p,E,m);null!==e&&(m=e,t.onEvent({type:"UPDATE_ANSWER",data:{text:m}})),p="",E=""}}if(g.trim()){const e=g.split("\n");for(const r of e)if(r.startsWith("event:"))p=r.substring(6).trim();else if(r.startsWith("data:")&&(E=r.substring(5).trim(),p&&E)){const e=this.processEvent(p,E,m);null!==e&&(m=e,t.onEvent({type:"UPDATE_ANSWER",data:{text:m}}))}}const f=this.createMessage("assistant",m);if(r.messages.push(f),r.updatedAt=Date.now(),"New Conversation"===r.title&&r.messages.length<=2){const e=await this.generateChatTitle(a.organizationId,a.conversationId,t.prompt);r.title=e,t.onEvent({type:"TITLE_UPDATE",data:{title:e,threadId:r.id}})}await this.saveThread(),t.onEvent({type:"DONE",data:{threadId:r.id}})}catch(r){this.handleModelError("Error sending message",e.ErrorCode.SERVICE_UNAVAILABLE,t,r)}}processEvent(t,r,a,o){if(!t||!r)return null;try{switch(t){case"completion":const s=JSON.parse(r);if(s.completion)return a+s.completion;break;case"error":const n=JSON.parse(r);if("rate_limit_error"!==n.type)return this.handleModelError(n.error||n.message||"Unknown Claude error",n.error||n.message?e.ErrorCode.SERVICE_UNAVAILABLE:e.ErrorCode.UNKNOWN_ERROR,o||void 0,n);try{n.message=JSON.parse(n.message);let t="";if(n.resetsAt){const e=new Date(1e3*n.resetsAt);n.resetsAt.resetsAtReadable=e.toLocaleString(),t=` Rate limit resets at ${n.resetsAt.resetsAtReadable}`}return this.handleModelError(`Claude rate limit exceeded.${t}`,e.ErrorCode.RATE_LIMIT_EXCEEDED,o||void 0,n)}catch(t){return this.handleModelError(`Claude rate limit exceeded: ${n.message}`,e.ErrorCode.RATE_LIMIT_EXCEEDED,o||void 0,n)}case"ping":return null;default:return console.log(`Unhandled event type: ${t}`,r),null}}catch(r){console.warn(`Error processing ${t} event:`,r),this.handleModelError(`Error processing ${t} event`,e.ErrorCode.UNKNOWN_ERROR,o,r)}return null}async fileToDataUrl(e){return new Promise((t,r)=>{const a=new FileReader;a.onload=()=>t(a.result),a.onerror=r,a.readAsDataURL(e)})}async editTitle(t,r){try{let a,o=!1!==r?.loadThread,s=!1!==r?.tryUpdateThread;if(r?.metadata&&(o=!1),o)await this.ensureThreadLoaded(),a=this.getClaudeMetadata();else{if(!r?.metadata)return this.handleModelError("No thread loaded and no metadata provided for sharing",e.ErrorCode.INVALID_REQUEST);if(a=r.metadata,!a.organizationId||!a.conversationId)return this.handleModelError("Invalid metadata provided for sharing",e.ErrorCode.INVALID_REQUEST)}const n=await fetch(`https://claude.ai/api/organizations/${a.organizationId}/chat_conversations/${a.conversationId}`,{method:"PUT",headers:this.getHeaders(),credentials:"include",body:JSON.stringify({name:t})});if(!n.ok)return this.handleModelError(`Failed to update title: ${n.status}`,e.ErrorCode.SERVICE_UNAVAILABLE,void 0,await n.text());s&&this.currentThread&&(this.currentThread.title=t,await this.saveThread())}catch(t){return this.handleModelError("Error updating conversation title",e.ErrorCode.SERVICE_UNAVAILABLE,void 0,t)}}async deleteServerThreads(t,r=!0,a=!0,o){try{if(o||(o=await this.getOrganizationId()),!o||!t)return this.handleModelError("Invalid metadata provided for request",e.ErrorCode.INVALID_REQUEST);const s=await fetch(`https://claude.ai/api/organizations/${o}/chat_conversations/delete_many`,{method:"POST",headers:this.getHeaders(),credentials:"include",body:JSON.stringify({conversation_uuids:t})});if(!s.ok)return this.handleModelError(`Failed to get conversation: ${s.status}`,e.ErrorCode.SERVICE_UNAVAILABLE,void 0,await s.text());let n=await s.json();if(r)for(let e of t)await this.deleteThread(e,a);return n}catch(t){return this.handleModelError("Error deleting conversation(s)",e.ErrorCode.SERVICE_UNAVAILABLE,void 0,t)}}async shareConversation(t){try{let r,a=!1!==t?.loadThread;if(t?.metadata&&(a=!1),a)await this.ensureThreadLoaded(),r=this.getClaudeMetadata();else{if(!t?.metadata)return this.handleModelError("No thread loaded and no metadata provided for sharing",e.ErrorCode.INVALID_REQUEST);if(r=t.metadata,!r.organizationId||!r.conversationId)return this.handleModelError("Invalid metadata provided for sharing",e.ErrorCode.INVALID_REQUEST)}const o=await fetch(`https://claude.ai/api/organizations/${r.organizationId}/chat_conversations/${r.conversationId}/share`,{method:"POST",headers:this.getHeaders(),credentials:"include",body:JSON.stringify({})});if(!o.ok){const t=await o.text();return this.handleModelError(`Failed to share conversation: ${o.status}`,e.ErrorCode.SERVICE_UNAVAILABLE,void 0,t)}const s=await o.json();if(!s.uuid)return this.handleModelError("Share response did not contain a URL",e.ErrorCode.SERVICE_UNAVAILABLE);const n=`https://claude.ai/share/${s.uuid}`;return a&&this.currentThread&&this.currentThread.metadata&&(this.currentThread.metadata.shareUrl=n,await this.saveThread()),n}catch(t){return this.handleModelError("Error sharing conversation",e.ErrorCode.SERVICE_UNAVAILABLE,void 0,t)}}findStyleByKey(e,t){if(e&&Array.isArray(e.defaultStyles))for(let r=0;r<e.defaultStyles.length;r++)if(e.defaultStyles[r]&&e.defaultStyles[r].key===t)return e.defaultStyles[r];if(e&&Array.isArray(e.customStyles))for(let r=0;r<e.customStyles.length;r++)if(e.customStyles[r]&&e.customStyles[r].key===t)return e.customStyles[r]}async getStyles(t){t||(t=await this.getOrganizationId());const r=await fetch(`https://claude.ai/api/organizations/${t}/list_styles`,{method:"GET",headers:this.getHeaders(),credentials:"include"});return r.ok?await r.json():this.handleModelError(`Failed to get styles: ${r.status}`,e.ErrorCode.SERVICE_UNAVAILABLE,void 0,await r.text())}async getConversationData(t){try{let r,a=!1!==t?.loadThread;if(t?.metadata&&(a=!1),a)await this.ensureThreadLoaded(),r=this.getClaudeMetadata();else{if(!t?.metadata)return this.handleModelError("No thread loaded and no metadata provided for getting conversation",e.ErrorCode.INVALID_REQUEST);if(r=t.metadata,!r.organizationId||!r.conversationId)return this.handleModelError("Invalid metadata provided for getting conversation",e.ErrorCode.INVALID_REQUEST)}const o=await fetch(`https://claude.ai/api/organizations/${r.organizationId}/chat_conversations/${r.conversationId}`,{method:"GET",headers:this.getHeaders(),credentials:"include"});return o.ok?await o.json():this.handleModelError(`Failed to get conversation: ${o.status}`,e.ErrorCode.SERVICE_UNAVAILABLE,void 0,await o.text())}catch(t){return this.handleModelError("Error getting conversation",e.ErrorCode.SERVICE_UNAVAILABLE,void 0,t)}}async getAllConversationsData(t){t||(t=await this.getOrganizationId());try{const r=await fetch(`https://claude.ai/api/organizations/${t}/chat_conversations`,{method:"GET",headers:this.getHeaders(),credentials:"include",redirect:"error",cache:"no-cache"});return r.ok?await r.json():this.handleModelError(`Failed to get conversations data: ${r.status}`,e.ErrorCode.SERVICE_UNAVAILABLE,void 0,await r.text())}catch(t){return this.handleModelError("Error getting conversations",e.ErrorCode.SERVICE_UNAVAILABLE,void 0,t)}}async getOrganizationData(t){t||(t=await this.getOrganizationId());try{const r=await fetch(`https://claude.ai/api/organizations/${t}`,{method:"GET",headers:this.getHeaders(),credentials:"include"});return r.ok?await r.json():this.handleModelError(`Failed to get organization data: ${r.status}`,e.ErrorCode.SERVICE_UNAVAILABLE,void 0,await r.text())}catch(t){return this.handleModelError("Error getting organization",e.ErrorCode.SERVICE_UNAVAILABLE,void 0,t)}}async getAllOrganizationsData(){try{const t=await fetch("https://claude.ai/api/organizations",{method:"GET",headers:this.getHeaders(),credentials:"include",redirect:"error",cache:"no-cache"});return 403===t.status?this.handleModelError("There is no logged-in Claude account in this browser.",e.ErrorCode.UNAUTHORIZED):t.ok?await t.json():this.handleModelError(`Failed to get organization data: ${t.status}`,e.ErrorCode.SERVICE_UNAVAILABLE,void 0,await t.text())}catch(t){return this.handleModelError("Error getting organization",e.ErrorCode.SERVICE_UNAVAILABLE,void 0,t)}}async getOrganizationId(){if(this.organizationId)return this.organizationId;try{const t=await this.getAllOrganizationsData();return t&&t.length?(this.organizationId=t[0].uuid,this.organizationId):this.handleModelError("No organizations found for Claude account",e.ErrorCode.UNAUTHORIZED)}catch(t){this.handleModelError("Claude webapp not available in your country or region",e.ErrorCode.SERVICE_UNAVAILABLE,void 0,t)}}}function Ee(e,t,r){return r}f([me,A("design:type",Function),A("design:paramtypes",[String,Object]),A("design:returntype",Promise)],pe.prototype,"editTitle",null),f([me,A("design:type",Function),A("design:paramtypes",[Array,Boolean,Boolean,Object]),A("design:returntype",Promise)],pe.prototype,"deleteServerThreads",null),f([me,A("design:type",Function),A("design:paramtypes",[Object]),A("design:returntype",Promise)],pe.prototype,"shareConversation",null),f([me,A("design:type",Function),A("design:paramtypes",[String]),A("design:returntype",Promise)],pe.prototype,"getStyles",null),f([me,A("design:type",Function),A("design:paramtypes",[Object]),A("design:returntype",Promise)],pe.prototype,"getConversationData",null),f([me,A("design:type",Function),A("design:paramtypes",[String]),A("design:returntype",Promise)],pe.prototype,"getAllConversationsData",null),f([me,A("design:type",Function),A("design:paramtypes",[String]),A("design:returntype",Promise)],pe.prototype,"getOrganizationData",null),f([me,A("design:type",Function),A("design:paramtypes",[]),A("design:returntype",Promise)],pe.prototype,"getAllOrganizationsData",null),f([me,A("design:type",Function),A("design:paramtypes",[]),A("design:returntype",Promise)],pe.prototype,"getOrganizationId",null);class fe extends g{constructor(e={}){super(),this.sessionKey=e.sessionKey,this.baseUrl="https://www.perplexity.ai",this.visitorId=u(),this.models={"Perplexity Sonar":["turbo","concise","non-reasoning","non-pro"],"Perplexity Pro Auto":["pplx_pro","copilot","non-reasoning","pro-limited"],"Perplexity Sonar Pro":["experimental","copilot","non-reasoning","pro-account"],"GPT-4.1":["gpt4o","copilot","non-reasoning","pro-account"],"Claude 3.7 Sonnet":["claude2","copilot","non-reasoning","pro-account"],"Gemini 2.5 Pro":["gemini2flash","copilot","non-reasoning","pro-account"],"Grok 3 Beta":["grok","copilot","non-reasoning","pro-account"],"Perplexity R1 1776":["r1","copilot","reasoning","pro-account"],"GPT-o4-mini":["o3mini","copilot","reasoning","pro-account"],"Claude 3.7 Sonnet Thinking":["claude37sonnetthinking","copilot","reasoning","pro-account"],"Perplexity Deep Research":["pplx_alpha","copilot","reasoning","pro-limited"]},this.defaultModel="Perplexity Sonar",this.initializeStorage().catch(console.error)}async initializeStorage(){(await this.getAllThreads()).length||await this.saveThreadsToStorage([]),await this.validateExistingThreads()}async validateExistingThreads(){const e=await this.getAllThreads();let t=!1;for(const r of e)r.modelName!==this.getName()||this.isValidPerplexityMetadata(r.metadata)||(await this.deleteThread(r.id),t=!0);t&&await this.saveThreadsToStorage(e.filter(e=>e.modelName!==this.getName()||this.isValidPerplexityMetadata(e.metadata)))}isValidPerplexityMetadata(e){return e?.conversationId}getName(){return"Perplexity Web"}getModels(){return this.models}getSearchSources(){return["web","scholar","social"]}supportsImageInput(){return!0}async uploadImage(t){try{const r=`${this.baseUrl}/rest/uploads/create_upload_url`,a={filename:t.name,content_type:t.type,source:"default",file_size:t.size,force_image:!1};if(!this.csrfToken&&(await this.checkAuth(),!this.csrfToken))return this.handleModelError("Failed to obtain CSRF token for upload",e.ErrorCode.UNAUTHORIZED);const o=await ne(r,{method:"POST",headers:this.getHeaders(!0),body:JSON.stringify(a)});if(!o||!o.s3_bucket_url||!o.fields)return this.handleModelError("Failed to get upload parameters from Perplexity",e.ErrorCode.UPLOAD_FAILED);const s=o.s3_bucket_url,n=new FormData;for(const e in o.fields)n.append(e,o.fields[e]);n.append("file",t);const i=await ne(s,{method:"POST",body:n});return i&&i.secure_url?(console.log("Image uploaded successfully:",i.secure_url),i.secure_url):this.handleModelError("Failed to upload image to Cloudinary or parse response",e.ErrorCode.UPLOAD_FAILED)}catch(t){return console.error("Perplexity image upload error:",t),this.handleModelError(`Image upload failed: ${t instanceof Error?t.message:String(t)}`,e.ErrorCode.UPLOAD_FAILED,void 0,t)}}getHeaders(e=!1){const t={"Content-Type":"application/json",Accept:"application/json","User-Agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"};return this.sessionKey&&(t.Cookie=`__Secure-next-auth.session-token=${this.sessionKey}`),e&&this.csrfToken&&(t["x-csrf-token"]=this.csrfToken),t}async checkAuth(){try{if(!await p(`${this.baseUrl}/`))return this.handleModelError(`Missing ${this.baseUrl} permission`,e.ErrorCode.MISSING_HOST_PERMISSION);const t=await ne(`${this.baseUrl}/api/auth/csrf`,{method:"GET",headers:this.getHeaders()});t&&t.csrfToken&&(this.csrfToken=t.csrfToken);const r=await ne(`${this.baseUrl}/api/auth/session`,{method:"GET",headers:this.getHeaders()});if(r&&r.user){this.userInfo={id:r.user.id,username:r.user.name,image:r.user.image,subscriptionStatus:r.user.subscription_status||"unknown"};try{const e=await ne(`${this.baseUrl}/rest/user/settings`,{method:"GET",headers:this.getHeaders(!0)});e&&(this.userSettings=e)}catch(e){console.warn("Failed to get user settings:",e)}return this.userInfo}return null}catch(t){return t instanceof Q&&401===t.status?null:this.handleModelError("Failed to check authentication with Perplexity",e.ErrorCode.SERVICE_UNAVAILABLE,void 0,t)}}async checkRateLimit(){try{return(await ne(`${this.baseUrl}/rest/rate-limit`,{method:"GET",headers:this.getHeaders(!0)})).remaining}catch(t){return this.handleModelError("Failed to check rate limit",e.ErrorCode.SERVICE_UNAVAILABLE,void 0,t)}}async getRecentThreads(){try{return(await ne(`${this.baseUrl}/rest/thread/list_recent`,{method:"GET",headers:this.getHeaders(!0)})).entries||[]}catch(t){return this.handleModelError("Failed to get recent threads",e.ErrorCode.SERVICE_UNAVAILABLE,void 0,t)}}async ensureThreadLoaded(){if(!this.currentThread){const e=(await this.getAllThreads()).filter(e=>e.modelName===this.getName()&&this.isValidPerplexityMetadata(e.metadata));if(e.length>0){const t=e.sort((e,t)=>t.updatedAt-e.updatedAt)[0];this.currentThread=t,console.log("Loaded existing thread from storage:",this.currentThread.id)}else await this.initNewThread()}}getPerplexityMetadata(){const t=this.getCurrentThreadSafe();if(!t.metadata)return this.handleModelError("No thread metadata available",e.ErrorCode.INVALID_REQUEST);const r=t.metadata;return r.conversationId?r:this.handleModelError("Invalid thread metadata",e.ErrorCode.INVALID_REQUEST)}getCurrentThreadSafe(){return this.currentThread?this.currentThread:this.handleModelError("No active thread",e.ErrorCode.INVALID_REQUEST)}async initNewThread(){try{await this.checkAuth();const e=u(),t=u();this.currentThread={id:e,title:"New Conversation",modelName:this.getName(),messages:[],createdAt:Date.now(),updatedAt:Date.now(),metadata:{conversationId:e,frontendContextUuid:t}},await this.saveThread()}catch(t){return this.handleModelError("Error initializing new thread",e.ErrorCode.METADATA_INITIALIZATION_ERROR,void 0,t)}}async loadThread(t){const r=(await this.getAllThreads()).find(e=>e.id===t);if(!r||r.modelName!==this.getName())return this.handleModelError(`Thread ${t} not found`,e.ErrorCode.INVALID_THREAD_ID);this.currentThread=r}async doSendMessage(t){let r=[],a=this.defaultModel;t.model?this.models[t.model]?a=t.model:console.warn(`Invalid model "${t.model}" provided. Using default model "${a}" instead.`):console.log(`No model provided, using default model "${a}".`);let o="internet";t.searchFocus?"internet"===t.searchFocus||"writing"===t.searchFocus?o=t.searchFocus:console.warn(`Invalid search focus "${t.searchFocus}" provided. Using default mode "internet" instead.`):console.log('No search focus provided, using default "internet" mode'),"internet"===t.searchFocus?t.searchSources?t.searchSources.every(e=>this.getSearchSources().includes(e))?r=t.searchSources:console.warn(`Invalid search source(s) "${t.searchSources}" provided. Using default source "web" instead.`):(console.log('No search source provided, using default source "web".'),r=["web"]):(t.searchSources||""!=t.searchSources)&&console.warn(`Invalid search source(s) "${t.searchSources}" provided for a no internet search response. Ignoring search sources.`);try{t.onEvent({type:"UPDATE_ANSWER",data:{text:""}}),await this.ensureThreadLoaded();const s=this.getCurrentThreadSafe();let n=[];if(t.images&&t.images.length>0){if(t.images.length>4)return this.handleModelError("Maximum of 4 images allowed per message.",e.ErrorCode.UPLOAD_AMOUNT_EXCEEDED,t);for(const r of t.images)try{const e=await this.uploadImage(r);n.push(e)}catch(a){return this.handleModelError(`Failed to upload image: ${r.name}`,e.ErrorCode.UPLOAD_FAILED,t,a)}}const i=this.createMessage("user",t.prompt);let d;n.length>0&&(i.metadata={...i.metadata||{},attachmentUrls:n}),s.messages.push(i);try{d=this.getPerplexityMetadata()}catch(r){if(!this.currentThread?.metadata)return this.handleModelError("Failed to initialize thread metadata",e.ErrorCode.METADATA_INITIALIZATION_ERROR,t,r);d=this.currentThread.metadata}const l=await this.checkAuth();if(await this.checkRateLimit()<=0)return this.handleModelError("You have reached your rate limit for Perplexity queries",e.ErrorCode.RATE_LIMIT_EXCEEDED);const c=u(),h=s.messages.length>1&&d.backendUuid&&d.readWriteToken,g={attachments:n,browser_history_summary:[],client_coordinates:null,frontend_uuid:c,is_incognito:!1,is_nav_suggestions_disabled:!1,is_related_query:!1,is_sponsored:!1,language:navigator.language||"en-US",mode:this.models[a][1],model_preference:this.models[a][0],prompt_source:"user",search_focus:o,search_recency_filter:null,send_back_text_in_streaming_api:!1,sources:r,supported_block_use_cases:["answer_modes","media_items","knowledge_cards","inline_entity_cards","place_widgets","finance_widgets","sports_widgets","shopping_widgets","jobs_widgets","search_result_widgets","entity_list_answer","todo_list"],timezone:Intl.DateTimeFormat().resolvedOptions().timeZone,use_schematized_api:!0,user_nextauth_id:l?.id,version:"2.18",visitor_id:this.visitorId};let m;h?(m={last_backend_uuid:d.backendUuid,read_write_token:d.readWriteToken,query_source:"followup"},console.log("Sending follow-up request with backendUuid:",d.backendUuid)):(m={frontend_context_uuid:d.frontendContextUuid||u(),query_source:"home"},console.log("Sending first request with frontendContextUuid:",m.frontend_context_uuid));const p={params:{...g,...m},query_str:t.prompt},E=await fetch(`${this.baseUrl}/rest/sse/perplexity_ask`,{method:"POST",headers:this.getHeaders(!0),body:JSON.stringify(p),signal:t.signal});if(!E.ok){const r=await E.text();return 429===E.status?this.handleModelError("Perplexity rate limit exceeded. Please try again later.",e.ErrorCode.RATE_LIMIT_EXCEEDED,t):this.handleModelError(`Perplexity API error: ${E.status} - ${r.substring(0,200)}`,e.ErrorCode.SERVICE_UNAVAILABLE,t)}if(!E.body)return this.handleModelError("Response body is null",e.ErrorCode.SERVICE_UNAVAILABLE,t);const f=E.body.getReader(),A=new TextDecoder;let y="",_="",T=d.threadUrlSlug||"",w=d.backendUuid||"",v=d.contextUuid||"",S=d.readWriteToken||"";for(;;){const{done:e,value:r}=await f.read();if(e)break;const a=A.decode(r,{stream:!0});let o;for(y+=a,console.log("Perplexity chunk received:",a.length);(o=y.indexOf("\n"))>=0;){const e=y.slice(0,o).trim();if(y=y.slice(o+1),e&&e.startsWith("event:")){const r=e.substring(6).trim();let a=y.indexOf("\n");if(!(a>=0)){y=e+"\n"+y;break}{const e=y.slice(0,a).trim();if(y=y.slice(a+1),e.startsWith("data:")){const a=e.substring(5).trim();if("end_of_stream"===r){console.log("Perplexity end_of_stream received");continue}if("message"===r)try{const e=JSON.parse(a);let r=!1;if(e.thread_url_slug&&!T&&(T=e.thread_url_slug,d.threadUrlSlug=T),e.backend_uuid&&!w&&(w=e.backend_uuid,d.backendUuid=w),e.context_uuid&&(v=e.context_uuid,d.contextUuid=v),e.blocks&&Array.isArray(e.blocks))for(const t of e.blocks)if("ask_text"===t.intended_usage&&t.markdown_block){const e=t.markdown_block;Array.isArray(e.chunks)&&1==e.chunks.length&&(_+=e.chunks[0],r=!0)}r&&(t.onEvent({type:"UPDATE_ANSWER",data:{text:_}}),console.log("Perplexity text update:",_.length)),!0!==e.final&&!0!==e.final_sse_message||(e.thread_title&&e.thread_title!==s.title&&(s.title=e.thread_title,t.onEvent({type:"TITLE_UPDATE",data:{title:e.thread_title,threadId:s.id}})),e.read_write_token&&(S=e.read_write_token,d.readWriteToken=S),e.related_queries&&Array.isArray(e.related_queries)&&t.onEvent({type:"SUGGESTED_RESPONSES",data:{suggestions:e.related_queries.map(e=>"string"==typeof e?e:e.text).filter(Boolean)}}))}catch(e){console.warn("Error parsing Perplexity message data JSON:",a,e)}}}}}}console.log("Perplexity stream finished, final text length:",_.length);const I=this.createMessage("assistant",_);s.messages.push(I),s.updatedAt=Date.now(),await this.saveThread(),t.onEvent({type:"DONE",data:{threadId:s.id}})}catch(r){return this.handleModelError("Error sending message",e.ErrorCode.SERVICE_UNAVAILABLE,t,r)}}async getModelVersion(){try{const t=await ne(`${this.baseUrl}/rest/version`,{method:"GET",headers:this.getHeaders(!0)});return t&&t.version?t.version:this.handleModelError("Invalid response from Perplexity version endpoint",e.ErrorCode.SERVICE_UNAVAILABLE)}catch(t){return this.handleModelError("Error fetching Perplexity version",e.ErrorCode.SERVICE_UNAVAILABLE,void 0,t)}}async deleteServerThreads(t,r=!0,a=!0){try{if(!await this.checkAuth())return this.handleModelError("You must be logged in to delete conversations",e.ErrorCode.UNAUTHORIZED);const o=await this.getAllThreads();for(const s of t)try{const t=o.find(e=>e.id===s);if(!t){console.warn(`Thread ${s} not found in storage.`);continue}if(t.modelName!==this.getName()){console.warn(`Thread ${s} has incorrect model name: ${t.modelName}`);continue}const n=t.metadata;if(!n||!n.backendUuid||!n.readWriteToken){r?(console.warn(`Thread ${s} has incomplete metadata. Cannot delete from server, deleting only from local storage.`),await this.deleteThread(s)):console.warn(`Thread ${s} has incomplete metadata. Cannot delete from server, skipping thread.`);continue}let i=await ne(`${this.baseUrl}/rest/thread/delete_thread_by_entry_uuid`,{method:"DELETE",headers:this.getHeaders(!0),body:JSON.stringify({entry_uuid:n.backendUuid,read_write_token:n.readWriteToken})});if(!i||"success"!==i.status)return this.handleModelError(`Failed to delete thread ${s} from Perplexity`,e.ErrorCode.SERVICE_UNAVAILABLE,void 0,i?.detail||"Unknown error");r&&await this.deleteThread(s,a)}catch(e){console.error(`Error deleting thread ${s}:`,e)}}catch(t){return this.handleModelError("Error deleting conversations",e.ErrorCode.SERVICE_UNAVAILABLE,void 0,t)}}async shareConversation(){try{await this.ensureThreadLoaded();const t=this.getPerplexityMetadata();if(!await this.checkAuth())return this.handleModelError("You must be logged in to share conversations",e.ErrorCode.UNAUTHORIZED);if(!t.contextUuid||!t.threadUrlSlug)return this.handleModelError("This conversation cannot be shared",e.ErrorCode.FEATURE_NOT_SUPPORTED);const r=await ne(`${this.baseUrl}/rest/thread/update_thread_access`,{method:"POST",headers:this.getHeaders(!0),body:JSON.stringify({context_uuid:t.contextUuid,updated_access:2,read_write_token:t.readWriteToken})});if("success"!==r.status||2!==r.access)return this.handleModelError("Failed to make conversation shareable",e.ErrorCode.SERVICE_UNAVAILABLE);return`${this.baseUrl}/search/${t.threadUrlSlug}`}catch(t){return this.handleModelError("Error sharing conversation",e.ErrorCode.SERVICE_UNAVAILABLE,void 0,t)}}async unShareConversation(){try{await this.ensureThreadLoaded();const t=this.getPerplexityMetadata();if(!await this.checkAuth())return this.handleModelError("You must be logged in to change conversation visibility",e.ErrorCode.UNAUTHORIZED);if(!t.contextUuid)return this.handleModelError("This conversation cannot be modified",e.ErrorCode.FEATURE_NOT_SUPPORTED);const r=await ne(`${this.baseUrl}/rest/thread/update_thread_access`,{method:"POST",headers:this.getHeaders(!0),body:JSON.stringify({context_uuid:t.contextUuid,updated_access:1,read_write_token:t.readWriteToken})});return"success"===r.status&&1===r.access}catch(t){return this.handleModelError("Error setting conversation to private",e.ErrorCode.SERVICE_UNAVAILABLE,void 0,t)}}async editTitle(t){try{await this.ensureThreadLoaded();if(!await this.checkAuth())return this.handleModelError("You must be logged in to edit conversation titles",e.ErrorCode.UNAUTHORIZED);const r=this.getPerplexityMetadata();if(!r.contextUuid)return void(this.currentThread&&(this.currentThread.title=t,await this.saveThread()));await ne(`${this.baseUrl}/rest/thread/set_thread_title`,{method:"POST",headers:this.getHeaders(!0),body:JSON.stringify({context_uuid:r.contextUuid,title:t,read_write_token:r.contextUuid})}),this.currentThread&&(this.currentThread.title=t,await this.saveThread())}catch(t){return this.handleModelError("Error updating conversation title",e.ErrorCode.SERVICE_UNAVAILABLE,void 0,t)}}}f([Ee,A("design:type",Function),A("design:paramtypes",[File]),A("design:returntype",Promise)],fe.prototype,"uploadImage",null),f([Ee,A("design:type",Function),A("design:paramtypes",[]),A("design:returntype",Promise)],fe.prototype,"checkAuth",null),f([Ee,A("design:type",Function),A("design:paramtypes",[]),A("design:returntype",Promise)],fe.prototype,"checkRateLimit",null),f([Ee,A("design:type",Function),A("design:paramtypes",[]),A("design:returntype",Promise)],fe.prototype,"getRecentThreads",null),f([Ee,A("design:type",Function),A("design:paramtypes",[Array,Boolean,Boolean]),A("design:returntype",Promise)],fe.prototype,"deleteServerThreads",null),f([Ee,A("design:type",Function),A("design:paramtypes",[]),A("design:returntype",Promise)],fe.prototype,"shareConversation",null),f([Ee,A("design:type",Function),A("design:paramtypes",[]),A("design:returntype",Promise)],fe.prototype,"unShareConversation",null),f([Ee,A("design:type",Function),A("design:paramtypes",[String]),A("design:returntype",Promise)],fe.prototype,"editTitle",null);let Ae=null,ye=null,_e=null,Te=new Uint8Array(0),we=new DataView(new ArrayBuffer(0));function ve(){if(!ye)throw new Error("WASM Memory not initialized for view update");Te.buffer!==ye.buffer&&(Te=new Uint8Array(ye.buffer),we=new DataView(ye.buffer))}const Se=new TextDecoder("utf-8",{ignoreBOM:!0,fatal:!0}),Ie=new TextEncoder;function be(e,t){return 0===t?"":(ve(),Se.decode(Te.subarray(e,e+t)))}let Ce=[void 0,null,!0,!1],Re=Ce.length;function Ne(e){Re===Ce.length&&Ce.push(Ce.length+1);const t=Re;return Re=Ce[t],Ce[t]=e,t}function xe(e){return Ce[e]}function Me(e){if(e<4)return;void 0!==Ce[e]&&(Ce[e]=Re,Re=e)}let Ue=0;function ke(e,t){const r=Ie.encode(e),a=t(r.length,1)>>>0;return ve(),Te.subarray(a,a+r.length).set(r),Ue=r.length,a}const De={__wbindgen_string_new:(e,t)=>Ne(be(e,t)),__wbindgen_object_clone_ref:e=>Ne(xe(e)),__wbindgen_object_drop_ref:Me,__wbindgen_throw:(e,t)=>{throw new Error(be(e,t))},__wbindgen_number_new:e=>Ne(e),__wbindgen_number_get:(e,t)=>{if(!ye)throw new Error("WASM memory not available");ve();const r=xe(e);"number"==typeof r?(we.setFloat64(t,r,!0),we.setInt32(t+8,0,!0)):(we.setFloat64(t,NaN,!0),we.setInt32(t+8,1,!0))},__wbindgen_is_undefined:e=>void 0===xe(e),__wbindgen_boolean_get:e=>{const t=xe(e);return"boolean"==typeof t?t?1:0:2},__wbindgen_string_get:(e,t)=>{if(!ye||!Ae?.__wbindgen_export_0)return console.error("WASM memory or malloc (__wbindgen_export_0) not available for __wbindgen_string_get"),void(ye&&(ve(),we.setInt32(t,0,!0),we.setInt32(t+4,0,!0)));ve();const r=xe(e);if("string"==typeof r){const e=ke(r,Ae.__wbindgen_export_0),a=Ue;we.setInt32(t,e,!0),we.setInt32(t+4,a,!0)}else we.setInt32(t,0,!0),we.setInt32(t+4,0,!0)},__wbindgen_error_new:(e,t)=>Ne(new Error(be(e,t))),__wbindgen_jsval_loose_eq:(e,t)=>xe(e)==xe(t),crypto_getRandomValues:(e,t)=>{ve(),crypto.getRandomValues(Te.subarray(e,e+t))},performance_now:()=>performance.now(),__wbindgen_thread_destroy:()=>{console.warn("STUB: __wbindgen_thread_destroy called")},__wbindgen_current_thread_destroy:()=>{console.warn("STUB: __wbindgen_current_thread_destroy called")},__wbindgen_thread_spawn:e=>(console.warn("STUB: __wbindgen_thread_spawn called with ptr:",e),0),__wbindgen_cb_drop:e=>"function"==typeof xe(e)&&(Me(e),!0)};async function Pe(e){if(console.log("Starting PoW solve using WASM (Ported JS Logic):",e),"DeepSeekHashV1"!==e.algorithm)throw new Error("Unsupported PoW algorithm: "+e.algorithm);if("number"!=typeof e.difficulty)throw new Error("Missing difficulty in challenge object");if("number"!=typeof e.expire_at)throw new Error("Missing expire_at in challenge object");const t=await async function(){return _e||(_e=(async()=>{if(Ae)return Ae;const e=n.runtime.getURL("assets/sha3_wasm_bg.7b9ca65ddd.wasm");console.log("Initializing DeepSeek WASM from:",e);try{const t=fetch(e);ye=new WebAssembly.Memory({initial:17,maximum:16384,shared:!1}),ve();const r={env:{...De,memory:ye}};console.log("Attempting WASM instantiation with imports:",Object.keys(r.env));const{instance:a}=await WebAssembly.instantiateStreaming(t,r);return a.exports.memory instanceof WebAssembly.Memory&&a.exports.memory!==ye?(console.log("WASM module exported its own memory instance. Updating reference."),ye=a.exports.memory,ve()):console.log("WASM using provided memory instance."),Ae=a.exports,console.log("DeepSeek WASM module loaded and initialized successfully."),Ce=[void 0,null,!0,!1],Re=Ce.length,Ae}catch(e){throw console.error("Failed to load or instantiate WASM module:",e),ye=null,Ae=null,_e=null,Ce=[void 0,null,!0,!1],Re=Ce.length,e}})(),_e)}();if(!t||!ye)throw new Error("WASM module is not initialized.");const{__wbindgen_export_0:r,__wbindgen_export_1:a,__wbindgen_add_to_stack_pointer:o,wasm_solve:s}=t;if(!(r&&a&&o&&s))throw console.error("Available WASM exports:",Object.keys(t)),new Error("Required WASM exports not found after loading.");ve();const{challenge:i,salt:d,difficulty:l,signature:c,target_path:h,expire_at:u}=e,g=i,m=`${d}_${u}_`;let p=0,E=0,f=0,A=0,y=0,_=!1,T=null;try{if(p=ke(g,r),f=Ue,E=ke(m,r),A=Ue,0===p||0===E){if(a){if(0!==p)try{a(p,f,1,1)}catch(e){}if(0!==E)try{a(E,A,1,1)}catch(e){}}throw new Error(`WASM malloc failed. Pointers: C=${p}, P=${E}`)}if(y=o(-16),y<=0)throw new Error(`WASM failed to adjust stack pointer correctly (returned ${y}).`);y%8!=0?console.warn(`Result stack pointer ${y} is not 8-byte aligned. Reading f64 might be problematic.`):y%4!=0&&console.warn(`Result stack pointer ${y} is not 4-byte aligned. Reading i32 might be problematic.`),console.log(`Calling wasm_solve with:\n          arg0 (resultStackPtr): ${y}\n          arg1 (challengePtr):   ${p} (len: ${f}) -> "${g}"\n          arg2 (challengeLen):   ${f}\n          arg3 (prefixPtr):      ${E} (len: ${A}) -> "${m}"\n          arg4 (prefixLen):      ${A}\n          arg5 (difficulty):     ${Number(l)}`);const t=performance.now();s(y,p,f,E,A,Number(l));const n=performance.now();console.log(`wasm_solve execution time: ${n-t} ms`);const u=(w=y,ve(),w<=0||w+4>we.byteLength?(console.error(`readInt32FromMemory: Invalid pointer ${w}. Buffer size: ${we.byteLength}`),0):(w%4!=0&&console.warn(`readInt32FromMemory: Pointer ${w} is not 4-byte aligned.`),we.getInt32(w,!0)));if(console.log(`WASM solve completed. Read status from stack [${y}]: ${u}`),0===u)throw console.error("WASM solve function indicated failure (status code 0)."),T=null,new Error("WASM solver failed internally (returned status 0).");{const e=function(e){return ve(),e<=0||e+8>we.byteLength?(console.error(`readFloat64FromMemory: Invalid pointer ${e}. Buffer size: ${we.byteLength}`),NaN):(e%8!=0&&console.warn(`readFloat64FromMemory: Pointer ${e} is not 8-byte aligned.`),we.getFloat64(e,!0))}(y+8);console.log(`WASM solve success (status ${u}). Read f64 nonce from stack [${y+8}]: ${e}`),T=Math.floor(e)}if(console.log(`Final calculated nonce: ${T}`),o(16),_=!0,null===T||"number"!=typeof T||isNaN(T))throw new Error(`PoW solver did not produce a valid number answer (got: ${T}).`);const v={algorithm:e.algorithm,challenge:i,salt:d,answer:T,signature:c,target_path:h||"/api/v0/chat/completion"},S=JSON.stringify(v);return btoa(S)}catch(e){if(console.error("Error caught during WASM PoW solve:",e),o&&y>0&&!_)try{o(16),console.log("Restored stack pointer in error handler.")}catch(e){console.error("Error restoring stack pointer after error:",e)}throw e}var w}function Oe(e,t,r){return r}class Le extends g{constructor(){super(),this.DEEPSEEK_HOST="https://chat.deepseek.com",this.initialize().catch(e=>{console.error("Failed to initialize DeepseekWebModel:",e)})}async initialize(){console.log("Initializing DeepSeek model...");try{const e=await E("Deepseek",this.DEEPSEEK_HOST,`${this.DEEPSEEK_HOST}/*`,"deepseekExtractor",!1);e?(this.authToken=`Bearer ${e}`,console.log("[Deepseek Init] Auth token loaded.")):console.warn("[Deepseek Init] Auth token not found during initial load."),await this.initializeStorage(),console.log("DeepSeek model ready.")}catch(t){this.handleModelError("Deepseek Initialization failed",e.ErrorCode.METADATA_INITIALIZATION_ERROR,void 0,t)}}async ensureAuthToken(){if(!this.authToken){console.log("[Deepseek ensureAuthToken] Auth token missing, attempting to retrieve...");const t=await E("Deepseek",this.DEEPSEEK_HOST,`${this.DEEPSEEK_HOST}/*`,"deepseekExtractor",!0);if(!t)return console.error("[Deepseek ensureAuthToken] Failed to retrieve auth token even after forcing."),this.handleModelError("DeepSeek authentication token is missing. Please log in to https://chat.deepseek.com.",e.ErrorCode.UNAUTHORIZED);this.authToken=`Bearer ${t}`}return this.authToken}async initializeStorage(){const e=await this.getAllThreads();e?0===e.length&&await this.saveThreadsToStorage([]):(console.warn("Could not retrieve threads from storage, starting fresh."),await this.saveThreadsToStorage([])),await this.validateExistingThreads()}async validateExistingThreads(){const e=await this.getAllThreads();if(!e)return;let t=!1;const r=[];for(const a of e)a.modelName===this.getName()?this.isValidDeepseekMetadata(a.metadata)?r.push(a):(console.warn(`Removing Deepseek thread ${a.id} due to invalid metadata.`),t=!0):r.push(a);t&&(await this.saveThreadsToStorage(r),console.log("Removed threads with invalid metadata."))}isValidDeepseekMetadata(e){return"string"==typeof e?.conversationId&&e.conversationId.length>0}getDeepseekMetadata(){const t=this.getCurrentThreadSafe();return t.metadata&&this.isValidDeepseekMetadata(t.metadata)?t.metadata:this.handleModelError("Invalid or missing Deepseek thread metadata.",e.ErrorCode.INVALID_METADATA)}async ensureThreadLoaded(){if(!this.currentThread){const e=(await this.getAllThreads()).filter(e=>e.modelName===this.getName()&&this.isValidDeepseekMetadata(e.metadata));if(e.length>0){const t=e.sort((e,t)=>t.updatedAt-e.updatedAt)[0];this.currentThread=t,console.log("Loaded existing DeepSeek thread from storage:",this.currentThread.id)}else await this.initNewThread()}}getCurrentThreadSafe(){return this.currentThread?this.currentThread:this.handleModelError("No active thread",e.ErrorCode.INVALID_REQUEST)}getName(){return"DeepSeek Web"}supportsImageInput(){return!1}async getHeaders(t=!0){let r={},a="x86",o="Windows",s="64";try{const e=await n.runtime.getPlatformInfo();a=e.arch||a,o="win"===e.os?"Windows":e.os,s="x86-64"===e.arch?"64":"32"}catch(e){}try{const e=navigator.userAgent,t=navigator.language||"en-US";r={Accept:"*/*","Accept-Encoding":"gzip, deflate, br, zstd","Accept-Language":navigator.languages?navigator.languages.join(","):t,"Cache-Control":"no-cache","Content-Type":"application/json",DNT:"1",Priority:"u=1, i","Sec-CH-UA":void 0!==navigator.userAgentData&&navigator.userAgentData.brands?navigator.userAgentData.brands.map(e=>`"${e.brand}";v="${e.version}"`).join(", "):'"Google Chrome";v="135", "Not-A.Brand";v="8", "Chromium";v="135"',"Sec-CH-UA-Arch":`"${a}"`,"Sec-CH-UA-Bitness":`"${s}"`,"Sec-CH-UA-Full-Version":void 0!==navigator.userAgentData&&navigator.userAgentData.uaFullVersion?`"${navigator.userAgentData.uaFullVersion}"`:'"135.0.7049.116"',"Sec-CH-UA-Full-Version-List":void 0!==navigator.userAgentData&&navigator.userAgentData.brands?navigator.userAgentData.brands.map(e=>`"${e.brand}";v="${e.version}"`).join(", "):'"Google Chrome";v="135.0.7049.116", "Not-A.Brand";v="*******", "Chromium";v="135.0.7049.116"',"Sec-CH-UA-Mobile":void 0!==navigator.userAgentData&&navigator.userAgentData.mobile?"?1":"?0","Sec-CH-UA-Model":'""',"Sec-CH-UA-Platform":`"${o}"`,"Sec-CH-UA-Platform-Version":'"19.0.0"',"Sec-Fetch-Dest":"empty","Sec-Fetch-Mode":"cors","Sec-Fetch-Site":"same-origin","User-Agent":e,"X-App-Version":`${(await this.getPlatformData())[0]}`,"X-Client-Locale":t.replace("-","_"),"X-Client-Platform":"web","X-Client-Version":"1.1.0-new-sse"}}catch(t){this.handleModelError("Failed to get platform info during header creation",e.ErrorCode.SERVICE_UNAVAILABLE,void 0,t)}if(t){if(!this.authToken)return this.handleModelError("Authentication token is missing when creating headers.",e.ErrorCode.UNAUTHORIZED);r.Authorization=this.authToken}return r}async getPowSolutionForCompletion(t){const r=`${this.DEEPSEEK_HOST}/api/v0/chat/create_pow_challenge`,a={target_path:t||"/api/v0/chat/completion"};let o;try{const t=(await this.makeDeepseekRequest(r,{method:"POST",headers:await this.getHeaders(),body:a,responseType:"json"}))._data;if(0!==t?.code||0!==t?.data?.biz_code||!t?.data?.biz_data?.challenge)return this.handleModelError("Invalid PoW challenge response structure.",e.ErrorCode.RESPONSE_PARSING_ERROR,void 0,t);o=t.data.biz_data.challenge}catch(t){return this.handleModelError("Failed to fetch PoW challenge.",e.ErrorCode.SERVICE_UNAVAILABLE,void 0,t)}try{return await Pe(o)}catch(t){return this.handleModelError("Failed to solve PoW challenge.",e.ErrorCode.POW_CHALLENGE_FAILED,void 0,t)}}async makeDeepseekRequest(t,r,a=1){try{if(!r.headers||!r.headers.Authorization){const e=await this.ensureAuthToken();r.headers={...r.headers,Authorization:e}}const e=await this.getHeaders(!1);r.headers={...e,...r.headers},console.debug(`Making Deepseek request: ${r.method||"GET"} ${t}`);const a=await ne.raw(t,r);return console.debug(`Deepseek request to ${t} successful.`),a}catch(r){if(r instanceof Q&&r.response){const a=r.response.status,o=r.response.text();try{const t=await o;if(403===a&&(t.includes("cf-challenge-running")||t.includes("Cloudflare")||t.includes("Checking if the site connection is secure")))return this.handleModelError("Cloudflare challenge detected. Please ensure you can access https://chat.deepseek.com in your browser.",e.ErrorCode.NETWORK_ERROR,void 0,r)}catch(e){console.warn(`Error reading response body for ${t} after initial failure:`,e)}}return this.handleModelError(`Deepseek API request to ${t} failed`,e.ErrorCode.NETWORK_ERROR,void 0,r)}}async getUserInfo(){const t=`${this.DEEPSEEK_HOST}/api/v0/users/current`;try{return(await this.makeDeepseekRequest(t,{method:"GET"}))._data}catch(t){return this.handleModelError("Failed to get user info",e.ErrorCode.NETWORK_ERROR,void 0,t)}}async getAllConversationsData(t=100){const r=`${this.DEEPSEEK_HOST}/api/v0/chat_session/fetch_page?count=${t}`;try{const t=await this.makeDeepseekRequest(r,{method:"GET"});if(0===t._data?.code&&0===t._data?.data?.biz_code&&t._data?.data?.biz_data?.chat_sessions)return t._data;{const r=t._data?.msg||t._data?.data?.biz_msg||"Unknown error fetching conversations";return this.handleModelError(`API returned unexpected structure or error: ${r}`,e.ErrorCode.RESPONSE_PARSING_ERROR,void 0,t._data)}}catch(t){return this.handleModelError("Failed to get conversations data",e.ErrorCode.NETWORK_ERROR,void 0,t)}}async getPlatformData(){let e=null,t=null;const r=`${this.DEEPSEEK_HOST}/version.txt`,a=`${this.DEEPSEEK_HOST}/downloads/status.json`;try{const t=await fetch(r,{method:"GET"});t.ok?e=await t.text():console.warn("Failed to fetch DeepSeek version:",t.status)}catch(e){console.warn("Failed to fetch DeepSeek version:",e)}try{const e=await fetch(a,{method:"GET"});e.ok?t=await e.json():console.warn("Failed to fetch DeepSeek status:",e.status)}catch(e){console.warn("Failed to fetch DeepSeek status:",e)}return[e,t]}async createConversation(){console.log("Crefrerefefr..");const t=`${this.DEEPSEEK_HOST}/api/v0/chat_session/create`;try{const r=(await this.makeDeepseekRequest(t,{method:"POST",body:{character_id:null}}))._data;if(0===r?.code&&0===r.data?.biz_code&&r.data?.biz_data?.id)return console.log(`Created new Deepseek conversation: ${r.data.biz_data.id}`),r.data.biz_data.id;{const t=r?.msg||r?.data?.biz_msg||"Unknown error creating conversation";return this.handleModelError(`Failed to create DeepSeek conversation: ${t}`,e.ErrorCode.SERVICE_UNAVAILABLE,void 0,r)}}catch(t){return this.handleModelError("Failed to create conversation",e.ErrorCode.SERVICE_UNAVAILABLE,void 0,t)}}async initNewThread(){console.log("ffffffffffffff..");try{await this.ensureAuthToken();const e=await this.createConversation();console.log(`Created new Deepseek conversation: ${e}`),this.currentThread={id:e,title:"New DeepSeek Chat",messages:[],createdAt:Date.now(),updatedAt:Date.now(),modelName:this.getName(),metadata:{conversationId:e,lastMessageId:null}},await this.saveThread(),console.log(`Initialized and saved new DeepSeek thread: ${this.currentThread.id} (Conv ID: ${e})`)}catch(t){return this.handleModelError("Failed to initialize new thread",e.ErrorCode.METADATA_INITIALIZATION_ERROR,void 0,t)}}async doSendMessage(t){let a,o=[];if(t.images&&t.images.length>0){if(t.images.length>4)return this.handleModelError("A maximum of 4 files can be uploaded at once.",e.ErrorCode.UPLOAD_AMOUNT_EXCEEDED,t);for(const e of t.images)try{const t=await this.uploadFile(e);o.push(t)}catch(e){return}}try{t.onEvent({type:"UPDATE_ANSWER",data:{text:""}}),await this.ensureThreadLoaded(),a=this.getCurrentThreadSafe();const r=this.getDeepseekMetadata(),s=this.createMessage("user",t.prompt);o.length>0&&(s.metadata={...s.metadata||{},uploadedFileIds:o}),a.messages.push(s),a.updatedAt=Date.now(),await this.saveThread();const n=await this.getPowSolutionForCompletion("/api/v0/chat/completion"),i={chat_session_id:r.conversationId,parent_message_id:r.lastMessageId||null,prompt:t.prompt,ref_file_ids:o,thinking_enabled:"reasoning"===t.mode,search_enabled:!0===t.searchEnabled};if(i.search_enabled&&o.length>0)return this.handleModelError("search mode and files attachments can not be used together",e.ErrorCode.INVALID_REQUEST,t);const d=`${this.DEEPSEEK_HOST}/api/v0/chat/completion`,l=await this.makeDeepseekRequest(d,{method:"POST",headers:{...await this.getHeaders(),Accept:"*/*","x-ds-pow-response":n},body:i,responseType:"stream",signal:t.signal});if(!l.body)return this.handleModelError("No response body received in stream.",e.ErrorCode.NETWORK_ERROR,t);const c=l.body.getReader(),h=new TextDecoder;let u="";const g={text:"",reasoningContent:"",tokensUsed:0,title:void 0,updatedAt:void 0,messageId:void 0,parentId:void 0,model:void 0,role:"ASSISTANT",thinkingEnabled:void 0,banEdit:void 0,banRegenerate:void 0,status:void 0,files:[],tips:[],insertedAt:void 0,searchEnabled:void 0,searchStatus:void 0,searchResults:void 0,reasoningElapsedSecs:void 0};let m=!1,p=null;for(;!m;){const{done:e,value:r}=await c.read();if(e)break;let o;for(u+=h.decode(r,{stream:!0});-1!==(o=u.indexOf("\n\n"));){const e=u.substring(0,o);u=u.substring(o+2);let r=null,s=null;const n=e.split("\n");for(const e of n)if(e.startsWith("event: "))r=e.substring(7).trim();else if(e.startsWith("data: ")){const t=e.substring(5).trim();if(t&&"[DONE]"!==t)try{s=JSON.parse(t)}catch(e){continue}}if("title"===r&&s?.content)g.title=s.content,t.onEvent({type:"TITLE_UPDATE",data:{title:g.title||"",threadId:a.id}});else if("update_session"===r&&s?.updated_at)g.updatedAt=1e3*s.updated_at;else if("close"===r)m=!0;else if("ready"===r);else if(!r&&s?.v&&"object"==typeof s.v&&s.v.response){const e=s.v.response;g.messageId=e.message_id,g.parentId=e.parent_id,g.model=e.model,g.role=e.role,g.thinkingEnabled=e.thinking_enabled,g.banEdit=e.ban_edit,g.banRegenerate=e.ban_regenerate,g.status=e.status,g.files=e.files||[],g.tips=e.tips||[],g.insertedAt=1e3*e.inserted_at,g.searchEnabled=e.search_enabled,g.searchStatus=e.search_status,g.searchResults=e.search_results,g.tokensUsed=e.accumulated_token_usage,g.reasoningContent=e.thinking_content||"",g.reasoningElapsedSecs=e.thinking_elapsed_secs||0}else if(!r&&s?.v&&"string"==typeof s.v){const e=s.p,r=s.v;let a=!1;"response/thinking_content"===e?(p="reasoning",g.reasoningContent=r,a=!0):"response/content"===e?(p="content",g.text=r,a=!0):e||null==r?e&&"response/status"!==e&&console.warn(`Received string data with unhandled path '${e}':`,s):"reasoning"===p?(g.reasoningContent+=r,a=!0):"content"===p?(g.text+=r,a=!0):g.thinkingEnabled?(g.reasoningContent+=r,p="reasoning",a=!0):(g.text+=r,p="content",a=!0),a&&t.onEvent({type:"UPDATE_ANSWER",data:{text:g.text,reasoningContent:g.reasoningContent,reasoningElapsedSecs:g.reasoningElapsedSecs}})}else if(!r&&s?.v&&"number"==typeof s.v){const e=s.p,r=s.v;"response/accumulated_token_usage"===e?g.tokensUsed=r:"response/thinking_elapsed_secs"===e&&(g.reasoningElapsedSecs=r,t.onEvent({type:"UPDATE_ANSWER",data:{text:g.text,reasoningContent:g.reasoningContent,reasoningElapsedSecs:g.reasoningElapsedSecs}}))}else!r&&s?.v&&"string"==typeof s.v&&"response/status"===s.p&&(g.status=s.v,g.status)}}const E=this.createMessage("assistant",g.text);g.reasoningContent&&g.reasoningContent.trim()&&(E.reasoningContent=g.reasoningContent.trim()),E.metadata={...E.metadata||{},responseTokens:g.tokensUsed,serverMessageId:g.messageId,serverParentId:g.parentId,modelUsed:g.model,finalStatus:g.status,reasoningTimeSecs:g.reasoningElapsedSecs,serverInsertedAt:g.insertedAt,thinkingEnabled:g.thinkingEnabled,searchEnabled:g.searchEnabled,searchStatus:g.searchStatus,searchResults:g.searchResults,banEdit:g.banEdit,banRegenerate:g.banRegenerate,files:g.files,tips:g.tips},Object.keys(E.metadata).forEach(e=>{void 0===E.metadata[e]&&delete E.metadata[e]}),a.messages.push(E),E.metadata?.serverMessageId&&(a.metadata||(a.metadata={conversationId:this.getDeepseekMetadata().conversationId}),a.metadata.lastMessageId=E.metadata.serverMessageId),g.title&&(a.title=g.title),g.updatedAt?a.updatedAt=g.updatedAt:a.updatedAt=Date.now(),await this.saveThread(),t.onEvent({type:"DONE",data:{threadId:a.id}})}catch(a){this.handleModelError("Error during message sending or processing",a instanceof r?a.code:e.ErrorCode.NETWORK_ERROR,t,a)}}async editTitle(t,r){try{let a,o=!1!==r?.loadThread,s=!1!==r?.tryUpdateThread;if(o)await this.ensureThreadLoaded(),a=this.getDeepseekMetadata();else{if(!r?.metadata)return this.handleModelError("No thread loaded and no metadata provided for title edit",e.ErrorCode.INVALID_REQUEST);if(a=r.metadata,!this.isValidDeepseekMetadata(a))return this.handleModelError("Invalid metadata provided for title edit",e.ErrorCode.INVALID_REQUEST)}const n=a.conversationId;if(!n)return this.handleModelError("Missing chat_session_id for title update",e.ErrorCode.INVALID_REQUEST);const i=`${this.DEEPSEEK_HOST}/api/v0/chat_session/update_title`,d={chat_session_id:n,title:t},l=(await this.makeDeepseekRequest(i,{method:"POST",headers:await this.getHeaders(),body:d,responseType:"json"}))._data;if(0!==l?.code||0!==l?.data?.biz_code){const t=l?.msg||l?.data?.biz_msg||"Unknown error updating title";return this.handleModelError(`Failed to update DeepSeek conversation title: ${t}`,e.ErrorCode.SERVICE_UNAVAILABLE,void 0,l)}s&&this.currentThread&&(this.currentThread.title=t,await this.saveThread())}catch(t){return this.handleModelError("Error updating DeepSeek conversation title",e.ErrorCode.SERVICE_UNAVAILABLE,void 0,t)}}async deleteServerThreads(t,r=!0,a=!0){try{const o=await this.getAllThreads();for(const s of t){const t=o.find(e=>e.metadata&&e.metadata.conversationId===s);if(!t){console.warn(`[deleteServerThreads] Thread ${s} not found locally.`);continue}if(t.modelName!==this.getName()){console.warn(`[deleteServerThreads] Thread ${s} has incorrect model name: ${t.modelName}. Skipping.`);continue}if(!this.isValidDeepseekMetadata(t.metadata)){r?(console.warn(`[deleteServerThreads] Thread ${s} has invalid or missing metadata. Cannot delete from server, deleting locally only.`),await this.deleteThread(t.id,a)):console.warn(`[deleteServerThreads] Thread ${s} has invalid or missing metadata. Cannot delete from server, skipping thread.`);continue}const n=t.metadata.conversationId,i=`${this.DEEPSEEK_HOST}/api/v0/chat_session/delete`,d={chat_session_id:n},l=(await this.makeDeepseekRequest(i,{method:"POST",headers:await this.getHeaders(),body:d,responseType:"json"}))._data;if(0!==l?.code||0!==l?.data?.biz_code){const t=l?.msg||l?.data?.biz_msg||"Unknown error deleting conversation";return this.handleModelError(`Failed to delete DeepSeek conversation: ${t}`,e.ErrorCode.SERVICE_UNAVAILABLE,void 0,l)}console.log(`[deleteServerThreads] Successfully deleted DeepSeek conversation: ${n} from server.`),r&&await this.deleteThread(t.id,a)}}catch(t){return this.handleModelError("Error deleting DeepSeek conversation(s)",e.ErrorCode.SERVICE_UNAVAILABLE,void 0,t)}}async uploadFile(t){await this.ensureAuthToken();const r=await this.getPowSolutionForCompletion("/api/v0/file/upload_file"),a=`${this.DEEPSEEK_HOST}/api/v0/file/upload_file`,o=new FormData;o.append("file",t);const s={};let n;this.authToken&&(s.Authorization=this.authToken),s["x-ds-pow-response"]=r;try{const e=await fetch(a,{method:"POST",headers:s,body:o,credentials:"include"});n=await e.json()}catch(t){return this.handleModelError("Failed to upload file to Deepseek",e.ErrorCode.UPLOAD_FAILED,void 0,t)}if(0!==n.code||0!==n.data?.biz_code){const t=n.data?.biz_msg||n.msg||"Unknown error uploading file";return this.handleModelError(`Deepseek file upload failed: ${t}`,e.ErrorCode.UPLOAD_FAILED,void 0,n)}let i=n.data?.biz_data;return i&&i.id?("PENDING"!==i.status&&"PARSING"!==i.status||(i=await this.pollFileStatus(i.id)),"CONTENT_EMPTY"===i.status?this.handleModelError("No text could be extracted from the image.",e.ErrorCode.UPLOAD_FAILED,void 0,i):"UNSUPPORTED"===i.status?this.handleModelError("File type not supported for Deepseek",e.ErrorCode.UPLOAD_FAILED,void 0,i):"SUCCESS"!==i.status?this.handleModelError(`File upload failed or not supported (status: ${i.status})`,e.ErrorCode.UPLOAD_FAILED,void 0,i):i.id):this.handleModelError("Deepseek file upload: missing file id in response",e.ErrorCode.UPLOAD_FAILED,void 0,n)}async pollFileStatus(t,r=10,a=1500){const o=`${this.DEEPSEEK_HOST}/api/v0/file/fetch_files?file_ids=${encodeURIComponent(t)}`;for(let e=0;e<r;++e){let e;await new Promise(e=>setTimeout(e,a));try{const t=await fetch(o,{method:"GET",credentials:"include",headers:this.authToken?{Authorization:this.authToken}:{}});e=await t.json()}catch(e){continue}if(0===e.code&&0===e.data?.biz_code&&e.data?.biz_data?.files?.[0]){const t=e.data.biz_data.files[0];if(["SUCCESS","CONTENT_EMPTY","FAILED","UNSUPPORTED"].includes(t.status))return t}}return this.handleModelError("File processing timed out on Deepseek",e.ErrorCode.UPLOAD_FAILED)}}return f([Oe,A("design:type",Function),A("design:paramtypes",[]),A("design:returntype",Promise)],Le.prototype,"getUserInfo",null),f([Oe,A("design:type",Function),A("design:paramtypes",[Number]),A("design:returntype",Promise)],Le.prototype,"getAllConversationsData",null),f([Oe,A("design:type",Function),A("design:paramtypes",[]),A("design:returntype",Promise)],Le.prototype,"getPlatformData",null),f([Oe,A("design:type",Function),A("design:paramtypes",[String,Object]),A("design:returntype",Promise)],Le.prototype,"editTitle",null),f([Oe,A("design:type",Function),A("design:paramtypes",[Array,Boolean,Boolean]),A("design:returntype",Promise)],Le.prototype,"deleteServerThreads",null),e.AIModelError=r,e.AUTH_EVENTS={TOKEN_REFRESH_START:"token_refresh_start",TOKEN_REFRESH_COMPLETE:"token_refresh_complete",TOKEN_REFRESH_ERROR:"token_refresh_error"},e.AbstractModel=g,e.BingWebModel=class extends g{constructor(){super(),this.baseUrl="https://copilot.microsoft.com",this.initializeStorage().catch(console.error)}async initializeStorage(){(await this.getAllThreads()).length||await this.saveThreadsToStorage([]),await this.validateExistingThreads()}async validateExistingThreads(){const e=await this.getAllThreads();let t=!1;for(const r of e)r.modelName!==this.getName()||this.isValidBingMetadata(r.metadata)||(await this.deleteThread(r.id),t=!0);t&&await this.saveThreadsToStorage(e.filter(e=>e.modelName!==this.getName()||this.isValidBingMetadata(e.metadata)))}isValidBingMetadata(e){return e?.conversationId}getName(){return"Bing Copilot"}supportsImageInput(){return!0}static injectedCopilotTokenExtractor(){for(let e=0;e<localStorage.length;e++)try{const t=localStorage.key(e);if(!t)continue;const r=JSON.parse(localStorage.getItem(t)||"");if(r&&"AccessToken"===r.credentialType&&r.expiresOn>Math.floor(Date.now()/1e3)&&r.target?.includes("ChatAI"))return console.log("[Copilot Inject] Token found:",r.secret),r.secret}catch(e){}return console.log("[Copilot Inject] No valid token found in localStorage."),null}async getCopilotToken(e=!1){return await E("Copilot",this.baseUrl,`${this.baseUrl}/*`,"copilotExtractor",e)}async ensureAuthToken(){if(!this.authToken){console.log("[Copilot ensureAuthToken] Token missing, retrieving...");const t=await this.getCopilotToken(!0);if(!t)return console.error("[Copilot ensureAuthToken] Failed to retrieve token."),this.handleModelError("Failed to get Copilot authorization token",e.ErrorCode.UNAUTHORIZED);this.authToken=`Bearer ${t}`,console.log("[Copilot ensureAuthToken] Token retrieved and set.")}return this.authToken}async createConversation(){try{if(!await p(`${this.baseUrl}/`))return this.handleModelError(`Missing ${this.baseUrl} permission`,e.ErrorCode.MISSING_HOST_PERMISSION);const t=await this.ensureAuthToken(),r=await fetch(`${this.baseUrl}/c/api/start`,{method:"POST",headers:{accept:"*/*","content-type":"application/json",authorization:t},body:JSON.stringify({timeZone:Intl.DateTimeFormat().resolvedOptions().timeZone,startNewConversation:!0}),credentials:"same-origin"});if(!r.ok){if(401===r.status)return console.log("Copilot createConversation returned 401. Token might be invalid/expired."),this.authToken=void 0,this.handleModelError("Authorization failed (401). Please try again.",e.ErrorCode.UNAUTHORIZED);if(!r.ok)return this.handleModelError(`Failed to create conversation: ${r.status}`,e.ErrorCode.SERVICE_UNAVAILABLE,void 0,await r.text())}const a=await r.json();return console.log("Copilot create conversation response:",a),a.currentConversationId?{conversationId:a.currentConversationId}:this.handleModelError("Failed to create Copilot conversation",e.ErrorCode.SERVICE_UNAVAILABLE)}catch(t){return console.error("Error initializing Copilot session:",t),this.handleModelError("Failed to initialize Copilot session",e.ErrorCode.NETWORK_ERROR,void 0,t)}}async doSendMessage(t){t.images&&t.images.length>1&&this.handleModelError("Bing Copilot only supports one image per message.",e.ErrorCode.UPLOAD_AMOUNT_EXCEEDED,t);let r=null,a=null;try{if(!await p(`wss://${new URL(this.baseUrl).hostname}/`))return this.handleModelError(`Missing ${this.baseUrl} permission`,e.ErrorCode.MISSING_HOST_PERMISSION);console.log("Full params received in doSendMessage:",JSON.stringify({prompt:t.prompt,hasImage:!!(t.images&&t.images.length>0),hasSignal:!!t.signal,hasOnEvent:!!t.onEvent,mode:t.mode})),console.log("Received mode:",t.mode);let o="chat";t.mode?"chat"===t.mode||"reasoning"===t.mode?(o=t.mode,console.log("Using validated mode:",o)):console.warn(`Invalid mode "${t.mode}" provided. Using default mode "chat" instead.`):console.log('No mode provided, using default "chat" mode'),t.onEvent({type:"UPDATE_ANSWER",data:{text:""}}),await this.ensureThreadLoaded();const s=this.getCurrentThreadSafe(),n=this.createMessage("user",t.prompt);let i;if(t.images&&t.images.length>0){t.images.length>1&&console.warn("BingCopilotWebModel currently only supports one image per message. Using the first image."),i=t.images[0];const r=await new Promise((r,a)=>{const o=new FileReader;o.onload=e=>r(e.target?.result),o.onerror=r=>{this.handleModelError("Failed to read image file",e.ErrorCode.UPLOAD_FAILED,t,r),a(new Error("Failed to read image file"))},o.readAsDataURL(i)});n.metadata={...n.metadata,imageDataUrl:r}}s.messages.push(n),await this.saveThread();const d=await this.getBingMetadata();let l,c;if(i)try{l=await this.uploadImage(i),c=this.baseUrl+l,console.log("Image uploaded successfully:",c),n.metadata={...n.metadata,fullUrl:c},await this.saveThread()}catch(r){this.handleModelError(`Failed to upload image: ${i.name}`,e.ErrorCode.UPLOAD_FAILED,t,r)}let h="",u=[],g=!1,m=!1;const E=s.messages.length<=1;let f=!E;const A=await this.ensureAuthToken(),y=A.startsWith("Bearer ")?A.substring(7):A;if(!y)return this.handleModelError("Could not extract raw token for WebSocket",e.ErrorCode.UNAUTHORIZED,t);r=new ge(`wss://${new URL(this.baseUrl).hostname}/c/api/chat?api-version=2&accessToken=${encodeURIComponent(y)}`);const _=setTimeout(()=>{r&&r.readyState!==ge.OPEN&&(console.error("WebSocket connection timeout"),r.close(),this.handleModelError("Connection timeout",e.ErrorCode.NETWORK_ERROR,t))},7e3),T=()=>{if(a&&(clearTimeout(a),a=null),r&&(r.readyState===ge.OPEN||r.readyState===ge.CONNECTING)){console.log("Safely closing WebSocket connection");try{r.close()}catch(e){console.error("Error closing WebSocket:",e)}}r=null},w=()=>{g&&(f&&m||!a)&&T()};r.onopen=()=>{console.log("WebSocket connection opened"),clearTimeout(_);try{const e=[];l&&e.push({type:"image",url:l}),e.push({type:"text",text:t.prompt});const a={event:"send",mode:o,conversationId:d.conversationId,content:e};console.log("Sending message:",JSON.stringify(a)),r.send(JSON.stringify(a))}catch(r){console.error("Error sending message:",r),T(),this.handleModelError("Failed to send message",e.ErrorCode.NETWORK_ERROR,t,r)}},r.onmessage=r=>{try{const e=JSON.parse(r.data);if(console.log("WebSocket message:",e),"received"===e.event)console.log("Message received by server:",e.messageId);else if("startMessage"===e.event)console.log("Assistant starting response:",e.messageId);else if("appendText"===e.event)h+=e.text||"",t.onEvent({type:"UPDATE_ANSWER",data:{text:h}});else if("done"===e.event){const e=this.createMessage("assistant",h);s.messages.push(e),s.updatedAt=Date.now(),this.saveThread(),t.onEvent({type:"DONE",data:{threadId:s.id}}),g=!0,a=setTimeout(()=>{console.log("Closing connection after timeout - did not receive all expected events"),T()},5e3)}else if("suggestedFollowups"===e.event&&e.suggestions){if(u=e.suggestions.map(e=>e),s.messages.length>0){const e=s.messages[s.messages.length-1];"assistant"===e.role&&(e.metadata={suggestedResponses:u},this.saveThread(),t.onEvent({type:"SUGGESTED_RESPONSES",data:{suggestions:u}}))}m=!0,w()}else E&&"titleUpdate"===e.event&&e.title&&(console.log("Received title update:",e.title),s.title=e.title,this.saveThread(),t.onEvent({type:"TITLE_UPDATE",data:{title:e.title,threadId:s.id}}),f=!0,w())}catch(r){this.handleModelError("Error parsing Copilot response",e.ErrorCode.RESPONSE_PARSING_ERROR,t,r)}},r.onerror=r=>{console.error("WebSocket error:",r),T(),this.handleModelError("WebSocket connection error",e.ErrorCode.NETWORK_ERROR,t,r)},r.onclose=r=>{console.log(`WebSocket connection closed with code ${r.code}`,r.reason),clearTimeout(_),a&&(clearTimeout(a),a=null),g||this.handleModelError(`Connection closed unexpectedly (${r.code})`,e.ErrorCode.NETWORK_ERROR,t)},t.signal&&t.signal.addEventListener("abort",()=>{console.log("Request aborted by user"),T()})}catch(o){if(a&&clearTimeout(a),r)try{r.close()}catch(e){console.error("Error closing WebSocket during error handling:",e)}this.handleModelError("Error in WebSocket communication",e.ErrorCode.NETWORK_ERROR,t,o)}}async ensureThreadLoaded(){if(!this.currentThread){const e=(await this.getAllThreads()).filter(e=>e.modelName===this.getName()&&this.isValidBingMetadata(e.metadata));if(e.length>0){const t=e.sort((e,t)=>t.updatedAt-e.updatedAt)[0];this.currentThread=t,console.log("Loaded existing thread from storage:",this.currentThread.id)}else await this.initNewThread()}}getCurrentThreadSafe(){return this.currentThread?this.currentThread:this.handleModelError("No active thread",e.ErrorCode.INVALID_REQUEST)}async initNewThread(){const e=await this.createConversation();this.currentThread={id:u(),title:"New Conversation",messages:[],createdAt:Date.now(),updatedAt:Date.now(),modelName:this.getName(),metadata:e},await this.saveThread()}async loadThread(t){const r=(await this.getAllThreads()).find(e=>e.id===t);if(!r||r.modelName!==this.getName())return this.handleModelError("Thread not found",e.ErrorCode.INVALID_THREAD_ID);this.currentThread=r,await this.saveThread()}async getBingMetadata(){const t=this.getCurrentThreadSafe();if(!t.metadata)return this.handleModelError("No thread metadata available",e.ErrorCode.INVALID_REQUEST);const r=t.metadata;return this.isValidBingMetadata(r)?r:await this.createConversation()}async saveThread(){if(!this.currentThread)return this.handleModelError("No active thread",e.ErrorCode.INVALID_REQUEST);await super.saveThread()}async uploadImage(t){try{const r=await this.ensureAuthToken(),a=await t.arrayBuffer();let o="image/jpeg";if(t.type)o=t.type;else{const e=t.name.toLowerCase();e.endsWith(".png")?o="image/png":e.endsWith(".gif")?o="image/gif":e.endsWith(".webp")?o="image/webp":e.endsWith(".bmp")?o="image/bmp":e.endsWith(".svg")&&(o="image/svg+xml")}const s={"content-type":o,authorization:r},n=await fetch(`${this.baseUrl}/c/api/attachments`,{method:"POST",headers:s,body:new Blob([a],{type:o}),credentials:"include",mode:"cors"});if(!n.ok){console.error("Image upload failed with status:",n.status);const t=await n.text();return console.error("Error response:",t),this.handleModelError(`Failed to upload image: ${n.status}`,e.ErrorCode.SERVICE_UNAVAILABLE)}const i=await n.json();return console.log("Image upload response:",i),i.url?i.url:this.handleModelError("Invalid image upload response",e.ErrorCode.SERVICE_UNAVAILABLE)}catch(t){return console.error("Error uploading image:",t),this.handleModelError("Failed to upload image to Copilot",e.ErrorCode.UPLOAD_FAILED,void 0,t)}}},e.ClaudeWebModel=pe,e.DeepseekWebModel=Le,e.GeminiWebModel=he,e.PerplexityWebModel=fe,e.clearAuthCache=async function(e){const t=e?`${m}${e}`:null;try{if(t)await n.storage.local.remove(t),console.log(`[${e} Auth] Cache cleared.`);else{const e=await n.storage.local.get(null),t=Object.keys(e).filter(e=>e.startsWith(m));t.length>0&&(await n.storage.local.remove(t),console.log("[Auth] All token caches cleared."))}}catch(t){console.warn(`[Auth] Error clearing cache for ${e||"all"}:`,t)}},e.configureAuth=function(e){void 0!==e.notifyTokenRefresh&&e.notifyTokenRefresh},e.copilotExtractor=function(){try{for(let e=0;e<localStorage.length;e++)try{const t=localStorage.key(e);if(!t)continue;const r=JSON.parse(localStorage.getItem(t)||"");if(r&&"AccessToken"===r.credentialType&&r.expiresOn>Math.floor(Date.now()/1e3)&&r.target?.includes("ChatAI"))return r.secret}catch(e){}}catch(e){console.error("Error executing injected Copilot token extractor:",e)}return null},e.deepseekExtractor=function(){try{const e=localStorage.getItem("userToken");if(e){const t=JSON.parse(e);if(t?.value&&"string"==typeof t.value&&t.value.length>0)return t.value;console.warn("Deepseek 'userToken' found but userToken structure not matched:",t)}else console.warn("Deepseek 'userToken' key not found in localStorage.")}catch(e){console.error("Error executing injected DeepSeek token extractor:",e)}return null},e.executeTokenRetrievalLogic=async function(e,t,r,a,o){let s=null;if(console.log(`[${e} Auth Internal] Attempting to retrieve token...`),void 0===n||!n.tabs||!n.scripting||!n.permissions)return console.error(`[${e} Auth Internal] Browser extension APIs (tabs, scripting, permissions) not available.`),null;let i=!1;try{i=await n.permissions.contains({origins:[r]})}catch(t){return console.error(`[${e} Auth Internal] Error checking permissions for ${r}:`,t),null}if(!i)return console.warn(`[${e} Auth Internal] Missing host permissions for ${r}. Cannot inject scripts or open tabs.`),null;if(o)console.log(`[${e} Auth Internal] Skipping existing tab check because forceNewTab is true.`);else try{const t=await n.tabs.query({url:r});console.log(`[${e} Auth Internal] Found ${t.length} potential tabs matching ${r}.`);for(const r of t)if(!r.id||r.url?.startsWith("chrome://")||r.url?.startsWith("about:"))console.log(`[${e} Auth Internal] Skipping tab ID: ${r.id} (missing ID or restricted URL: ${r.url})`);else{console.log(`[${e} Auth Internal] Attempting to inject script into tab ID: ${r.id} (URL: ${r.url})`);try{const t=await n.scripting.executeScript({target:{tabId:r.id},func:a,world:"MAIN"});if(t&&t[0]&&t[0].result){s=t[0].result,console.log(`[${e} Auth Internal] Successfully extracted token from existing tab: ${r.id}`);break}console.log(`[${e} Auth Internal] Script executed on tab ${r.id}, but no token found in result.`,t)}catch(t){t instanceof Error&&t.message.includes("No window matching")?console.warn(`[${e} Auth Internal] Could not inject into tab ${r.id} (window likely closed).`):t instanceof Error&&t.message.includes("Could not establish connection")?console.warn(`[${e} Auth Internal] Connection error injecting into tab ${r.id} (possibly devtools?).`):t instanceof Error&&t.message.includes("Cannot access contents of the page")?console.warn(`[${e} Auth Internal] Cannot access contents of tab ${r.id} (URL: ${r.url}). Might be restricted page.`):console.warn(`[${e} Auth Internal] Failed to inject script or extract token from tab ${r.id}:`,t)}}}catch(t){console.error(`[${e} Auth Internal] Error querying for tabs matching ${r}:`,t)}if(!s){if(console.log(`[${e} Auth Internal] No token from existing tabs. Proceeding with temporary window method...`),!n.windows)return console.error(`[${e} Auth Internal] Browser.windows API is not available. Cannot open temporary window.`),null;let r=null;try{console.log(`[${e} Auth Internal] Creating temporary window for ${t}...`),r=await n.windows.create({url:t,focused:!1,type:"popup",width:150,height:150}),console.log(`[${e} Auth Internal] Temporary window created (ID: ${r?.id})`);const o=r?.tabs?.[0]?.id;if(!o)throw new Error("Failed to get tab ID from temporary window.");console.log(`[${e} Auth Internal] Temporary tab ID: ${o}`),console.log(`[${e} Auth Internal] Waiting for temporary tab to load...`),await new Promise(e=>setTimeout(e,4e3)),console.log(`[${e} Auth Internal] Attempting REAL extractor injection into temp tab ID: ${o}`);try{const t=await n.scripting.executeScript({target:{tabId:o},func:a,world:"MAIN"});console.log(`[${e} Auth Internal] REAL extractor script executed. Results:`,t),t&&t[0]&&t[0].result?(s=t[0].result,console.log(`[${e} Auth Internal] Successfully extracted REAL token via temporary tab.`)):console.warn(`[${e} Auth Internal] Failed to extract REAL token via temporary tab.`,t)}catch(t){console.error(`[${e} Auth Internal] Error injecting REAL extractor script:`,t)}}catch(t){console.error(`[${e} Auth Internal] Error creating/accessing temporary tab:`,t)}finally{if(console.log(`[${e} Auth Internal] Entering finally block for temporary window.`),r?.id){console.log(`[${e} Auth Internal] Attempting to close temporary window ID: ${r.id}`);try{await n.windows.remove(r.id),console.log(`[${e} Auth Internal] Successfully closed temporary auth window.`)}catch(t){t instanceof Error&&t.message.includes("No window with id")?console.log(`[${e} Auth Internal] Temporary window already closed.`):console.warn(`[${e} Auth Internal] Error closing temporary auth window:`,t)}}else console.log(`[${e} Auth Internal] No temporary window ID found to close in finally block.`)}}return s?console.log(`[${e} Auth Internal] Token retrieval successful.`):console.warn(`[${e} Auth Internal] Failed to retrieve token after all attempts.`),s},e.file2base64=function(e,t=!1){return new Promise((r,a)=>{const o=new FileReader;o.readAsDataURL(e),o.onload=()=>{const e=o.result;r(t?e:e.split(",")[1])},o.onerror=a})},e.getCookiesForDomain=async function(e){try{const t=await n.cookies.getAll({domain:e}),r={};for(const e of t)r[e.name]=e.value;return r}catch(t){return console.error(`Error getting cookies for ${e}:`,t),{}}},e.getTokenFromWebsite=E,e.parseSSEResponse=async function(e,t){const r=e.body.getReader(),a=new TextDecoder;let o="";for(;;){const{done:e,value:s}=await r.read();if(e)break;o+=a.decode(s,{stream:!0});const n=o.split("\n");o=n.pop()||"";for(const e of n){const r=e.trim();if(r&&r.startsWith("data: ")){t(r.slice(6))}}}if(o.trim()&&o.startsWith("data: ")){t(o.slice(6))}},e.requestHostPermission=p,e.streamAsyncIterable=async function*(e){const t=e.getReader();try{for(;;){const{done:e,value:r}=await t.read();if(e)return;yield r}}finally{t.releaseLock()}},e}({});
