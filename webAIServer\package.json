{"name": "webai-server", "version": "0.1.0", "private": true, "description": "Local WebAI server exposing OpenAI-compatible endpoints and bridging to the WebAI browser extension via WebSocket.", "license": "MIT", "type": "module", "main": "server.js", "scripts": {"start": "node server.js", "dev": "node server.js", "test": "node --test tests/models-and-chat.test.js"}, "dependencies": {"cors": "^2.8.5", "express": "^4.19.2", "ws": "^8.18.0", "uuid": "^9.0.1"}}