<!doctype html>
<html>

<head>
    <meta charset="utf-8" />
    <title>WebAI Settings</title>
    <link rel="stylesheet" href="options.css" />
</head>

<body>
    <div class="container">
        <header>
            <h1>WebAI Settings</h1>
            <div id="wsStatus" class="badge">Disconnected</div>
        </header>

        <section>
            <h2>Server Connection</h2>
            <div class="server-config">
                <div class="input-group">
                    <label for="wsUrl">WebSocket URL</label>
                    <div class="input-row">
                        <input id="wsUrl" type="text" placeholder="ws://localhost:11434/ws" />
                        <button id="saveWs" class="btn secondary">Save</button>
                    </div>
                </div>
                <div class="actions-row">
                    <button id="refreshModels" class="btn primary">Refresh Models</button>
                </div>
            </div>
        </section>
        <section>
            <h2>API Keys</h2>
            <div id="keysStatus" class="note">Connecting to server...</div>
            <div class="row">
                <button id="createKey" class="btn primary">Create key</button>
                <button id="deleteSelected" class="btn danger" disabled>Delete selected</button>
            </div>
            <div class="card">
                <ul id="keysList" class="list"></ul>
            </div>
        </section>

        <section>
            <h2>Provider Defaults</h2>
            <div class="provider-defaults">
                <div class="provider-group">
                    <h3>Perplexity</h3>
                    <div class="input-group">
                        <label for="pplx-searchFocus">Search Focus</label>
                        <select id="pplx-searchFocus">
                            <option value="">Default (internet)</option>
                            <option value="internet">Internet</option>
                            <option value="writing">Writing</option>
                        </select>
                    </div>
                    <div class="input-group" id="pplx-sources-group">
                        <label>Search Sources (for Internet focus)</label>
                        <div class="checkbox-group">
                            <label><input type="checkbox" id="pplx-source-web" value="web"> Web</label>
                            <label><input type="checkbox" id="pplx-source-scholar" value="scholar"> Scholar</label>
                            <label><input type="checkbox" id="pplx-source-social" value="social"> Social</label>
                        </div>
                    </div>
                </div>

                <div class="provider-group">
                    <h3>Deepseek</h3>
                    <div class="input-group">
                        <label>
                            <input type="checkbox" id="deepseek-searchEnabled"> Enable Search
                        </label>
                    </div>
                </div>

                <div class="provider-group">
                    <h3>Claude</h3>
                    <div class="input-group">
                        <label for="claude-styleKey">Style</label>
                        <select id="claude-styleKey">
                            <option value="">Default</option>
                            <option value="creative">Creative</option>
                            <option value="balanced">Balanced</option>
                            <option value="precise">Precise</option>
                        </select>
                        <small class="note">Styles are loaded dynamically when available</small>
                    </div>
                </div>
            </div>
        </section>

        <section>
            <h2>Available Models</h2>
            <ul id="models"></ul>
        </section>

        <section>
            <h2>Thread Management</h2>
            <div class="thread-management">
                <div class="row">
                    <button id="refreshThreads" class="btn secondary">Refresh Threads</button>
                    <button id="newThread" class="btn primary">New Thread</button>
                </div>
                <div class="thread-list">
                    <div id="threadsStatus" class="note">Click Refresh Threads to load conversations</div>
                    <ul id="threadsList" class="list"></ul>
                </div>
            </div>
        </section>

        <footer>
            <small>WebAI v0.1.0</small>
        </footer>
    </div>

    <script src="options.js" type="module"></script>
</body>

</html>