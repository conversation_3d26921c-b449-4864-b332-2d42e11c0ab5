// WebAI Extension Background (Service Worker)
// - Manages WS connection to local server
// - Instantiates AI-Models-Bridge models and routes requests

import {
    executeTokenRetrieval<PERSON>ogic,
    deepseekExtractor,
    copilotExtractor,
    GeminiWebModel,
    ClaudeWebModel,
    PerplexityWebModel,
    DeepseekWebModel,
    BingWebModel,
    configure<PERSON>uth,
    AUTH_EVENTS
} from './lib/ai-models-bridge.esm.js';


// Configure authentication system
configureAuth({ notifyTokenRefresh: true });

// Listen for authentication events
// document.addEventListener(AUTH_EVENTS.TOKEN_REFRESH_START, (event) => {
//     console.log('Token refresh started:', event.detail);
// });

// document.addEventListener(AUTH_EVENTS.TOKEN_REFRESH_COMPLETE, (event) => {
//     console.log('Token refresh completed:', event.detail);
// });

// document.addEventListener(AUTH_EVENTS.TOKEN_REFRESH_ERROR, (event) => {
//     console.error('Token refresh error:', event.detail);
// });

// Provider defaults persistence
const DEFAULTS_KEY = 'webai_provider_defaults';
let providerDefaults = {};
chrome.storage.local.get([DEFAULTS_KEY], (res) => {
    providerDefaults = res[DEFAULTS_KEY] || {};
});
chrome.storage.onChanged.addListener((changes, area) => {
    if (area === 'local' && changes[DEFAULTS_KEY]) {
        providerDefaults = changes[DEFAULTS_KEY].newValue || {};
    }
});


const WS_URL_KEY = 'webai_ws_url';
const DEFAULT_WS_URL = 'ws://localhost:11434/ws';

// --- Management channel (keys over encrypted WS) ---
let mgmtReady = false;
let mgmtAESKey = null; // CryptoKey for AES-GCM
let mgmtWaiters = new Map(); // reqId -> {resolve, reject}

function b64ToBuf(b64) { return Uint8Array.from(atob(b64), c => c.charCodeAt(0)).buffer; }
function bufToB64(buf) { const bytes = new Uint8Array(buf); let s = ''; for (const b of bytes) s += String.fromCharCode(b); return btoa(s); }

async function mgmtHandleHello(msg) {
    try {
        const serverPubRaw = b64ToBuf(msg.pub);
        const serverNonce = new Uint8Array(b64ToBuf(msg.nonce));
        const keyPair = await crypto.subtle.generateKey({ name: 'ECDH', namedCurve: 'P-256' }, true, ['deriveBits']);
        const serverPubKey = await crypto.subtle.importKey('raw', serverPubRaw, { name: 'ECDH', namedCurve: 'P-256' }, false, []);
        const sharedBits = await crypto.subtle.deriveBits({ name: 'ECDH', public: serverPubKey }, keyPair.privateKey, 256);
        const clientNonce = crypto.getRandomValues(new Uint8Array(16));
        const material = new Uint8Array([...new Uint8Array(sharedBits), ...serverNonce, ...clientNonce]);
        const digest = await crypto.subtle.digest('SHA-256', material);
        mgmtAESKey = await crypto.subtle.importKey('raw', digest, { name: 'AES-GCM', length: 256 }, false, ['encrypt', 'decrypt']);
        const clientPubRaw = await crypto.subtle.exportKey('raw', keyPair.publicKey);
        ws.send(JSON.stringify({ type: 'mgmt.reply', pub: bufToB64(clientPubRaw), nonce: bufToB64(clientNonce.buffer) }));
    } catch (e) {
        console.warn('Mgmt handshake failed:', e);
    }
}

async function mgmtEncrypt(obj) {
    const iv = crypto.getRandomValues(new Uint8Array(12));
    const pt = new TextEncoder().encode(JSON.stringify(obj));
    const ct = await crypto.subtle.encrypt({ name: 'AES-GCM', iv }, mgmtAESKey, pt); // includes auth tag
    return { iv: bufToB64(iv.buffer), data: bufToB64(ct) };
}
async function mgmtDecrypt(payload) {
    const iv = new Uint8Array(b64ToBuf(payload.iv));
    const data = new Uint8Array(b64ToBuf(payload.data));
    const pt = await crypto.subtle.decrypt({ name: 'AES-GCM', iv }, mgmtAESKey, data);
    return JSON.parse(new TextDecoder().decode(pt));
}

function sendMgmt(op, extra = {}) {
    return new Promise(async (resolve, reject) => {
        if (!mgmtAESKey || !ws) return reject(new Error('Mgmt channel not ready'));
        const reqId = Math.random().toString(36).slice(2);
        mgmtWaiters.set(reqId, { resolve, reject });
        try {
            const payload = await mgmtEncrypt({ op, reqId, ...extra });
            ws.send(JSON.stringify({ type: 'mgmt.enc', payload }));
        } catch (e) {
            mgmtWaiters.delete(reqId);
            reject(e);
        }
        setTimeout(() => {
            if (mgmtWaiters.has(reqId)) {
                mgmtWaiters.delete(reqId);
                reject(new Error('Mgmt request timeout'));
            }
        }, 10000);
    });
}

let ws = null;
let connected = false;
// Dynamically import ESM exports for auth/token retrieval utilities

// Handle token retrieval requests from library utils (getTokenFromWebsite)
chrome.runtime.onMessage.addListener((message, _sender, sendResponse) => {
    if (!message || !message.type) return false;

    if (message.type === 'GET_AUTH_TOKEN_FROM_WEBSITE') {
        const { serviceName, targetUrl, urlPattern, extractorName, forceNewTab } = message.payload || {};
        if (!serviceName || !targetUrl || !urlPattern || !extractorName) {
            sendResponse({ success: false, error: 'Invalid payload received by background script.' });
            return false;
        }
        const extractorMap = { deepseekExtractor, copilotExtractor };
        const extractorFunc = extractorMap[extractorName];
        if (!extractorFunc) {
            sendResponse({ success: false, error: `Unknown extractor function: ${extractorName}` });
            return false;
        }
        executeTokenRetrievalLogic(serviceName, targetUrl, urlPattern, extractorFunc, !!forceNewTab)
            .then((token) => sendResponse({ success: true, token: token || null }))
            .catch((err) => sendResponse({ success: false, error: err?.message || String(err) }));
        return true; // async response
    }

    if (message.type === 'PING') {
        sendResponse({ success: true, message: 'PONG' });
        return false;
    }

    if (message.type === 'MGMT_OP') {
        const { op, extra } = message;
        if (!mgmtAESKey || !ws) {
            sendResponse({ ok: false, error: 'Management channel not ready' });
            return false;
        }
        sendMgmt(op, extra).then((inner) => {
            sendResponse(inner);
        }).catch((e) => {
            sendResponse({ ok: false, error: e?.message || String(e) });
        });
        return true;
    }

    if (message.type === 'GET_MODELS') {
        ensureModels();
        sendResponse({ models: modelsList });
        return false;
    }

    if (message.type === 'GET_WS_STATUS') {
        sendResponse({ connected });
        return false;
    }

    return false; // not handled
});
// Helpers: extract images from OpenAI-compatible chat messages
function extFromMime(mime) {
    if (!mime || typeof mime !== 'string') return '';
    const map = { 'image/png': 'png', 'image/jpeg': 'jpg', 'image/jpg': 'jpg', 'image/webp': 'webp', 'image/gif': 'gif' };
    return map[mime] || '';
}

async function fileFromUrl(url, suggestedName = 'image') {
    try {
        // Try direct fetch first
        const resp = await fetch(url);
        if (!resp.ok) throw new Error(`Failed to fetch image (${resp.status})`);
        const blob = await resp.blob();
        const ext = extFromMime(blob.type) || 'bin';
        const name = suggestedName + '.' + ext;
        return new File([blob], name, { type: blob.type || 'application/octet-stream' });
    } catch (directError) {
        console.warn('Direct fetch failed, trying proxy:', directError.message);

        // Fallback to server proxy for restricted origins
        try {
            const wsUrlValue = await new Promise(resolve => {
                chrome.storage.local.get(['webai_ws_url'], res => {
                    resolve(res.webai_ws_url || 'ws://localhost:11434/ws');
                });
            });
            const baseUrl = wsUrlValue.replace('ws://', 'http://').replace('/ws', '');
            const proxyUrl = `${baseUrl}/proxy-image?url=${encodeURIComponent(url)}`;

            const resp = await fetch(proxyUrl);
            if (!resp.ok) throw new Error(`Proxy fetch failed (${resp.status})`);
            const blob = await resp.blob();
            const ext = extFromMime(blob.type) || 'bin';
            const name = suggestedName + '.' + ext;
            return new File([blob], name, { type: blob.type || 'application/octet-stream' });
        } catch (proxyError) {
            console.error('Both direct and proxy fetch failed:', proxyError.message);
            throw new Error(`Failed to fetch image: ${directError.message} (proxy also failed: ${proxyError.message})`);
        }
    }
}

async function getImageFilesFromMessages(messages, maxImages = 4) {
    const out = [];
    if (!Array.isArray(messages)) return out;
    for (let i = messages.length - 1; i >= 0 && out.length < maxImages; i--) {
        const m = messages[i];
        if (!m || m.role !== 'user') continue;
        const c = m.content;
        if (Array.isArray(c)) {
            for (const part of c) {
                if (out.length >= maxImages) break;
                if (part && part.type === 'image_url' && part.image_url && typeof part.image_url.url === 'string') {
                    const url = part.image_url.url;
                    try {
                        out.push(await fileFromUrl(url));
                    } catch (e) {
                        console.warn('Failed to resolve image_url for chat message:', e);
                    }
                }
                if (part && part.type === 'input_image' && typeof part.data === 'string') {
                    const url = part.data;
                    try {
                        out.push(await fileFromUrl(url));
                    } catch (e) {
                        console.warn('Failed to resolve input_image for chat message:', e);
                    }
                }
            }
        }
    }
    return out;
}



// Models and routing
let ModelManager = null;
let modelsList = [];
const requestControllers = new Map(); // requestId -> AbortController

function connectWS() {
    chrome.storage.local.get([WS_URL_KEY], (res) => {
        const url = res[WS_URL_KEY] || DEFAULT_WS_URL;
        try {
            ws = new WebSocket(url);
        } catch (e) {
            console.warn('Failed to create WS', e);
            scheduleReconnect();
            return;
        }
        ws.onopen = () => {
            connected = true;
            console.log('WebAI WS connected');
            ws.send(JSON.stringify({ type: 'hello', extensionVersion: '0.1.0' }));
            ensureModels();
            ws.send(JSON.stringify({ type: 'models.list', models: modelsList }));
            // Notify options page of connection status
            chrome.runtime.sendMessage({ type: 'WS_STATUS', connected: true }, () => {
                if (chrome.runtime.lastError) {
                    // Ignore - options page might not be open
                }
            });
        };
        ws.onmessage = async (ev) => {
            let msg;
            try { msg = JSON.parse(ev.data); } catch { return; }
            const { type, requestId } = msg || {};
            if (type === 'mgmt.hello') {
                await mgmtHandleHello(msg);
                return;
            }
            if (type === 'mgmt.ready') {
                mgmtReady = true;
                // Send MGMT_READY only once when handshake completes
                chrome.runtime.sendMessage({ type: 'MGMT_READY' }, () => {
                    if (chrome.runtime.lastError) {
                        // Ignore - options page might not be open
                    }
                });
                return;
            }
            if (type === 'mgmt.enc.resp' && msg.payload) {
                if (!mgmtAESKey) return;
                try {
                    const inner = await mgmtDecrypt(msg.payload);
                    const { reqId } = inner || {};
                    if (reqId && mgmtWaiters.has(reqId)) {
                        mgmtWaiters.get(reqId).resolve(inner);
                        mgmtWaiters.delete(reqId);
                    }
                } catch (e) { /* ignore */ }
                return;
            }
            if (type === 'request.models') {
                ensureModels();
                return ws.send(JSON.stringify({ type: 'response.models', requestId, models: modelsList }));
            }

            // Thread management requests
            if (type === 'request.threads.list') {
                try {
                    const allThreads = [];
                    for (const [modelKey, instance] of Object.entries(ModelManager.instances)) {
                        if (typeof instance.getAllThreads === 'function') {
                            const threads = await instance.getAllThreads();
                            allThreads.push(...threads.map(t => ({ ...t, modelKey })));
                        }
                    }
                    return ws.send(JSON.stringify({ type: 'response.result', requestId, result: { threads: allThreads } }));
                } catch (error) {
                    return ws.send(JSON.stringify({ type: 'response.error', requestId, error: { message: error.message, code: 'THREAD_LIST_FAILED' } }));
                }
            }

            if (type === 'request.threads.new') {
                try {
                    const { model } = msg;
                    const [provider] = model.split(':');
                    const instance = ModelManager.instances[provider];
                    if (!instance || typeof instance.initNewThread !== 'function') {
                        throw new Error(`Model ${provider} does not support thread management`);
                    }
                    await instance.initNewThread();
                    const thread = instance.getCurrentThread();
                    return ws.send(JSON.stringify({ type: 'response.result', requestId, result: { thread } }));
                } catch (error) {
                    return ws.send(JSON.stringify({ type: 'response.error', requestId, error: { message: error.message, code: 'THREAD_CREATE_FAILED' } }));
                }
            }

            if (type === 'request.threads.load') {
                try {
                    const { threadId, model } = msg;
                    const [provider] = model.split(':');
                    const instance = ModelManager.instances[provider];
                    if (!instance || typeof instance.loadThread !== 'function') {
                        throw new Error(`Model ${provider} does not support thread management`);
                    }
                    await instance.loadThread(threadId);
                    const thread = instance.getCurrentThread();
                    return ws.send(JSON.stringify({ type: 'response.result', requestId, result: { thread } }));
                } catch (error) {
                    return ws.send(JSON.stringify({ type: 'response.error', requestId, error: { message: error.message, code: 'THREAD_LOAD_FAILED' } }));
                }
            }

            if (type === 'request.threads.delete') {
                try {
                    const { threadId, model } = msg;
                    const [provider] = model.split(':');
                    const instance = ModelManager.instances[provider];
                    if (!instance || typeof instance.deleteThread !== 'function') {
                        throw new Error(`Model ${provider} does not support thread management`);
                    }
                    await instance.deleteThread(threadId);
                    return ws.send(JSON.stringify({ type: 'response.result', requestId, result: { deleted: true } }));
                } catch (error) {
                    return ws.send(JSON.stringify({ type: 'response.error', requestId, error: { message: error.message, code: 'THREAD_DELETE_FAILED' } }));
                }
            }
            if (type === 'request.start' && msg.endpoint === 'chat.completions') {
                return handleChatCompletion(requestId, msg.payload);
            }
            if (type === 'request.abort') {
                const ctrl = requestControllers.get(requestId);
                if (ctrl) {
                    try { ctrl.abort(); } catch { }
                    requestControllers.delete(requestId);
                }
                return;
            }
        };
        ws.onclose = () => {
            connected = false;
            console.log('WebAI WS closed');
            // Notify options page of disconnection
            chrome.runtime.sendMessage({ type: 'WS_STATUS', connected: false }, () => {
                if (chrome.runtime.lastError) {
                    // Ignore - options page might not be open
                }
            });
            scheduleReconnect();
        };
        ws.onerror = () => { /* handled by close */ };
    });
}

function scheduleReconnect() {
    setTimeout(connectWS, 2000);
}

function ensureModels() {
    if (ModelManager) return;

    const instances = {
        'gemini-web': new GeminiWebModel(),
        'claude-web': new ClaudeWebModel(),
        'pplx-web': new PerplexityWebModel(),
        'deepseek-web': new DeepseekWebModel(),
        'bing-web': new BingWebModel()
    };

    function buildModelsList() {
        const out = [];
        // Gemini variants from instance.models keys
        try {
            const gm = instances['gemini-web'];
            const keys = Object.keys(gm.models || {});
            for (const k of keys) out.push(`gemini-web:${k}`);
        } catch { }
        // Perplexity from getModels
        try {
            const pm = instances['pplx-web'];
            const pmodels = (typeof pm.getModels === 'function') ? pm.getModels() : {};
            for (const k of Object.keys(pmodels)) out.push(`pplx-web:${k}`);
        } catch { }
        // Deepseek modes
        out.push('deepseek-web:chat');
        out.push('deepseek-web:reasoning');
        out.push('deepseek-web:chat-search');
        out.push('deepseek-web:reasoning-search');
        // Bing modes
        out.push('bing-web:chat');
        out.push('bing-web:reasoning');
        // Claude single canonical
        out.push('claude-web:sonnet');
        return out;
    }

    modelsList = buildModelsList();

    ModelManager = {
        instances,
        resolve(modelId) {
            const [provider, variant] = modelId.split(':');
            return { provider, variant, instance: instances[provider] };
        }
    };
}

// Smart conversation matching and thread management
async function handleConversationMatching(instance, incomingMessages, modelId) {
    if (!instance || typeof instance.getAllThreads !== 'function') {
        console.log('Model does not support thread management, using current thread');
        return;
    }

    // Get all threads for this model
    const allThreads = await instance.getAllThreads();
    const modelThreads = allThreads.filter(t => t.modelName === instance.getName());

    console.log(`[ConversationMatching] Checking ${incomingMessages.length} incoming messages against ${modelThreads.length} existing threads`);

    // Check if this is a new conversation (1-2 messages, starting fresh)
    const isNewConversation = incomingMessages.length <= 2 &&
        (incomingMessages.length === 1 ||
            (incomingMessages.length === 2 && incomingMessages[0].role === 'system' && incomingMessages[1].role === 'user'));

    console.log(`[ConversationMatching] isNewConversation: ${isNewConversation}, messageCount: ${incomingMessages.length}`);
    if (incomingMessages.length > 0) {
        console.log(`[ConversationMatching] Message roles: [${incomingMessages.map(m => m.role).join(', ')}]`);
    }

    if (isNewConversation) {
        console.log('[ConversationMatching] Detected new conversation, creating new thread');
        await instance.initNewThread();

        // Mark that we have a system message to combine with user message
        if (incomingMessages.length === 2 && incomingMessages[0].role === 'system') {
            // Store system message for combining with user message in sendMessage
            instance._pendingSystemMessage = incomingMessages[0].content;
            console.log('[ConversationMatching] System message will be combined with user message');
        }
        return;
    }

    // For longer conversations, try to match against existing threads (with performance optimization)
    // IMPORTANT: Exclude the last message since that's the new message being sent
    const messagesToMatch = incomingMessages.slice(0, -1);
    console.log(`[ConversationMatching] Matching ${messagesToMatch.length} messages (excluding current message) against existing threads`);

    const bestMatch = findBestThreadMatchOptimized(messagesToMatch, modelThreads);

    if (bestMatch) {
        console.log(`[ConversationMatching] Found matching thread: ${bestMatch.thread.id} (score: ${bestMatch.score})`);
        await instance.loadThread(bestMatch.thread.id);
    } else {
        console.log('[ConversationMatching] No matching thread found, creating new thread');
        await instance.initNewThread();

        // For multi-message conversations that don't match, add all previous messages to context
        const currentThread = instance.getCurrentThread();
        if (currentThread && messagesToMatch.length > 0) {
            // Collapse first two messages if system+user, to match prompt and matching logic
            function collapseSystemUser(messages) {
                if (messages.length >= 2 && messages[0].role === 'system' && messages[1].role === 'user') {
                    return [
                        {
                            role: 'system+user',
                            content: normalizeMessageContent(messages[0].content) + '\n\n' + normalizeMessageContent(messages[1].content)
                        },
                        ...messages.slice(2)
                    ];
                }
                return messages;
            }
            const collapsedMessages = collapseSystemUser(messagesToMatch);
            // Only add up to the last message in the collapsed array (exclude the last, which is the new message)
            const contextToAdd = collapsedMessages.slice(0, -1);
            for (let i = 0; i < contextToAdd.length; i++) {
                const msg = contextToAdd[i];
                currentThread.messages.push({
                    id: generateId(),
                    role: msg.role,
                    content: msg.content,
                    timestamp: Date.now() - (contextToAdd.length - i) * 1000 // Stagger timestamps
                });
            }
            await instance.saveThread();
            console.log(`[ConversationMatching] Added ${contextToAdd.length} context messages to new thread`);
        }
    }
}

function findBestThreadMatchOptimized(incomingMessages, threads) {
    if (threads.length === 0) return null;

    // Performance optimization: limit search scope
    const maxThreadsToCheck = Math.min(threads.length, 20); // Only check 20 most recent threads
    const recentThreads = threads
        .sort((a, b) => (b.updatedAt || b.createdAt) - (a.updatedAt || a.createdAt))
        .slice(0, maxThreadsToCheck);

    let bestMatch = null;
    let bestScore = 0;
    const minScore = 0.7; // Require 70% match confidence

    for (const thread of recentThreads) {
        if (!thread.messages || thread.messages.length === 0) continue;
        if (thread.messages.length > incomingMessages.length * 3) continue; // still skip extremely long threads

        // Allow prefix matching when thread has fewer messages than incoming
        const score = calculateThreadMatchScoreOptimized(incomingMessages, thread.messages);
        if (score > bestScore && score > minScore) {
            bestScore = score;
            bestMatch = { thread, score };

            // Early exit for perfect matches
            if (score >= 0.95) break;
        }
    }

    return bestMatch;
}

// Helper function to normalize message content (handle both string and array formats)
function normalizeMessageContent(content) {
    if (typeof content === 'string') {
        return content;
    }
    if (Array.isArray(content)) {
        return content.map(part => {
            if (typeof part === 'string') return part;
            if (part && typeof part === 'object' && part.text) return part.text;
            if (part && typeof part === 'object' && part.type === 'text' && part.text) return part.text;
            return '';
        }).join('\n');
    }
    return String(content || '');
}

function calculateThreadMatchScoreOptimized(incomingMessages, threadMessages) {
    if (!threadMessages || threadMessages.length === 0) return 0;
    if (incomingMessages.length === 0) return 0;

    console.log(`[ThreadMatching] Comparing ${incomingMessages.length} incoming vs ${threadMessages.length} thread messages`);

    // Collapse first two messages if they are system+user, to match how prompt is sent
    function collapseSystemUser(messages) {
        if (messages.length >= 2 && messages[0].role === 'system' && messages[1].role === 'user') {
            return [
                {
                    role: 'system+user',
                    content: normalizeMessageContent(messages[0].content) + '\n\n' + normalizeMessageContent(messages[1].content)
                },
                ...messages.slice(2)
            ];
        }
        return messages;
    }

    const collapsedIncomingFull = collapseSystemUser(incomingMessages);
    const collapsedThreadFull = collapseSystemUser(threadMessages);

    // If thread shorter, compare only up to its length (prefix match attempt)
    const effectiveIncoming = collapsedThreadFull.length < collapsedIncomingFull.length
        ? collapsedIncomingFull.slice(0, collapsedThreadFull.length)
        : collapsedIncomingFull;

    // Only check the first N (after potential truncation)
    const maxMessagesToCheck = Math.min(effectiveIncoming.length, 10);
    const incomingSequence = effectiveIncoming.slice(0, maxMessagesToCheck).map(m => ({
        role: m.role,
        content: normalizeMessageContent(m.content)
    }));

    const collapsedThread = collapsedThreadFull; // rename for existing code below

    console.log(`[ThreadMatching] Incoming sequence roles: [${incomingSequence.map(m => m.role).join(', ')}]`);
    console.log(`[ThreadMatching] First incoming content preview: "${incomingSequence[0]?.content?.substring(0, 100)}..."`);

    let bestScore = 0;
    // If thread shorter than incomingSequence length (after truncation this should not happen)
    if (collapsedThread.length < incomingSequence.length) {
        // Direct prefix compare only (already truncated incomingSequence to thread length earlier if shorter)
        let matches = 0;
        let sequenceBroken = false;
        for (let i = 0; i < collapsedThread.length; i++) {
            const incoming = incomingSequence[i];
            const thread = collapsedThread[i];
            console.log(`[ThreadMatching] (Prefix) Comparing pos ${i}: incoming(${incoming.role}) vs thread(${thread.role})`);
            if (incoming.role === thread.role && incoming.content === thread.content) {
                matches++;
            } else if (incoming.role === thread.role) {
                const sim = calculateContentSimilarityFast(incoming.content, thread.content);
                if (sim > 0.8) matches += sim; else { sequenceBroken = true; break; }
            } else { sequenceBroken = true; break; }
        }
        return sequenceBroken ? 0 : (matches / collapsedThread.length);
    }

    for (let startPos = 0; startPos <= collapsedThread.length - incomingSequence.length; startPos++) {
        const threadSubsequence = collapsedThread.slice(startPos, startPos + incomingSequence.length).map(m => ({
            role: m.role,
            content: normalizeMessageContent(m.content)
        }));

        let matches = 0;
        let sequenceBroken = false;

        for (let i = 0; i < incomingSequence.length; i++) {
            const incoming = incomingSequence[i];
            const thread = threadSubsequence[i];

            console.log(`[ThreadMatching] Comparing pos ${i}: incoming(${incoming.role}) vs thread(${thread.role})`);

            // Role/content equivalence helper (treat system+user as matching a user-only stored message if suffix matches)
            function rolesEquivalent(inRole, thRole, inContent, thContent) {
                if (inRole === thRole) return true;
                if (inRole === 'system+user' && thRole === 'user') {
                    // Accept if the thread's user content appears at end of combined content
                    if (typeof inContent === 'string' && typeof thContent === 'string') {
                        if (inContent.endsWith(thContent)) return true;
                        // Also try splitting combined by double newline
                        const parts = inContent.split(/\n\n+/);
                        const lastPart = parts[parts.length - 1];
                        if (lastPart && lastPart.trim() === thContent.trim()) return true;
                    }
                }
                return false;
            }

            if (rolesEquivalent(incoming.role, thread.role, incoming.content, thread.content) && incoming.content === thread.content) {
                matches++;
                console.log(`[ThreadMatching] Exact match at position ${i}`);
            } else {
                // If roles (or equivalent) match but content differs slightly, give partial credit
                if (rolesEquivalent(incoming.role, thread.role, incoming.content, thread.content)) {
                    const contentSimilarity = calculateContentSimilarityFast(incoming.content, thread.content);
                    console.log(`[ThreadMatching] Content similarity at position ${i}: ${contentSimilarity.toFixed(2)}`);
                    if (contentSimilarity > 0.8) {
                        matches += contentSimilarity;
                    } else {
                        sequenceBroken = true;
                        break;
                    }
                } else {
                    console.log(`[ThreadMatching] Role mismatch at position ${i}: ${incoming.role} vs ${thread.role}`);
                    sequenceBroken = true;
                    break;
                }
            }
        }

        if (!sequenceBroken) {
            const score = matches / incomingSequence.length;
            if (score > bestScore) {
                bestScore = score;
                console.log(`[ThreadMatching] Found better match at position ${startPos}: ${score.toFixed(2)}`);
            }
        }
    }

    return bestScore;
}

function calculateContentSimilarityFast(content1, content2) {
    if (content1 === content2) return 1.0;
    if (!content1 || !content2) return 0;

    // Fast similarity check: first check length difference
    const lengthRatio = Math.min(content1.length, content2.length) / Math.max(content1.length, content2.length);
    if (lengthRatio < 0.5) return 0; // Very different lengths

    // Quick character-based similarity for short content
    if (content1.length < 100 && content2.length < 100) {
        const shorter = content1.length < content2.length ? content1 : content2;
        const longer = content1.length < content2.length ? content2 : content1;
        return longer.includes(shorter) ? 0.9 : 0;
    }

    // For longer content, use word-based similarity but limit word count
    const words1 = content1.toLowerCase().split(/\s+/).slice(0, 20); // Only first 20 words
    const words2 = content2.toLowerCase().split(/\s+/).slice(0, 20);
    const commonWords = words1.filter(word => words2.includes(word));

    return commonWords.length / Math.max(words1.length, words2.length);
}

function generateId() {
    return Math.random().toString(36).substring(2) + Date.now().toString(36);
}
// Approximate token count (splits on whitespace and punctuation)
function countTokens(text) {
    if (!text) return 0;
    // Split on whitespace and most punctuation
    return (text.match(/\w+|[^\s\w]/g) || []).length;
}

async function handleChatCompletion(requestId, payload) {
    ensureModels();
    const { model: modelId, messages, stream, stream_options } = payload || {};
    const includeUsage = !!(stream_options && stream_options.include_usage);
    const imageFiles = await getImageFilesFromMessages(messages);
    if (!ModelManager.instances) return;
    const { provider, variant, instance } = ModelManager.resolve(modelId);
    if (!instance) {
        return ws?.send(JSON.stringify({ type: 'response.error', requestId, error: { message: `Unknown model ${modelId}` } }));
    }

    try {
        // Smart conversation matching and thread management
        await handleConversationMatching(instance, messages, modelId);
    } catch (error) {
        console.warn('Conversation matching failed:', error.message);
        // Continue with current thread if matching fails
    }

    // Build prompt from last user message, combining with system message if needed
    const lastUser = [...messages].reverse().find(m => m.role === 'user');
    let prompt = normalizeMessageContent(lastUser?.content);

    // Combine system message with user message for new conversations
    if (instance._pendingSystemMessage) {
        prompt = `${instance._pendingSystemMessage}\n\n${prompt}`;
        delete instance._pendingSystemMessage; // Clear after use
        console.log('[ConversationMatching] Combined system and user messages');
    }

    const options = buildOptionsForVariant(provider, variant);

    // --- Token counting ---
    // Prompt tokens: sum all message contents
    let prompt_tokens = 0;
    if (Array.isArray(messages)) {
        for (const m of messages) {
            if (m && m.content) {
                if (typeof m.content === 'string') prompt_tokens += countTokens(m.content);
                else if (Array.isArray(m.content)) prompt_tokens += countTokens(m.content.join(' '));
            }
        }
    }
    let completion_tokens = 0;
    let total_tokens = 0;

    // Stream mapping
    let streamedText = '';
    const onEvent = (evt) => {
        if (!ws) return;
        if (evt.type === 'UPDATE_ANSWER') {
            const text = (evt && evt.data && typeof evt.data.text === 'string') ? evt.data.text : '';
            streamedText += text;
            ws.send(JSON.stringify({ type: 'response.chunk', requestId, data: { text } }));
        }
        if (evt.type === 'DONE') {
            // On stream end, send usage if requested
            if (includeUsage) {
                completion_tokens = countTokens(streamedText);
                total_tokens = prompt_tokens + completion_tokens;
                ws.send(JSON.stringify({ type: 'response.usage', requestId, usage: { prompt_tokens, completion_tokens, total_tokens } }));
            }
            ws.send(JSON.stringify({ type: 'response.end', requestId }));
            requestControllers.delete(requestId);
        }
        if (evt.type === 'ERROR') {
            ws.send(JSON.stringify({ type: 'response.error', requestId, error: { message: evt.error?.message || 'Unknown error', code: evt.error?.code } }));
            requestControllers.delete(requestId);
        }
    };

    try {
        const controller = new AbortController();
        requestControllers.set(requestId, controller);
        // Provider-specific image constraints
        let images = imageFiles;
        if (provider === 'gemini-web' && images.length > 1) images = images.slice(0, 1);

        if (stream) {
            await instance.sendMessage(prompt, { ...options, signal: controller.signal, images: images.length ? images : undefined, onEvent });
        } else {
            let finalText = '';
            await instance.sendMessage(prompt, { ...options, signal: controller.signal, images: images.length ? images : undefined, onEvent: (e) => { if (e.type === 'UPDATE_ANSWER') finalText = (e && e.data && typeof e.data.text === 'string') ? e.data.text : finalText; if (e.type === 'ERROR') onEvent(e); } });
            requestControllers.delete(requestId);
            if (includeUsage) {
                completion_tokens = countTokens(finalText);
                total_tokens = prompt_tokens + completion_tokens;
                ws.send(JSON.stringify({ type: 'response.result', requestId, result: { text: finalText }, usage: { prompt_tokens, completion_tokens, total_tokens } }));
            } else {
                ws.send(JSON.stringify({ type: 'response.result', requestId, result: { text: finalText } }));
            }
        }
    } catch (e) {
        requestControllers.delete(requestId);
        ws?.send(JSON.stringify({ type: 'response.error', requestId, error: { message: e?.message || String(e), code: e?.code } }));
    }
}

function buildOptionsForVariant(provider, variant) {
    const opts = {};
    const defaults = providerDefaults?.[provider] || {};
    if (provider === 'bing-web') {
        if (variant === 'reasoning') opts.mode = 'reasoning';
        else opts.mode = 'chat';
    }
    if (provider === 'deepseek-web') {
        if (variant.includes('reasoning')) opts.mode = 'reasoning';
        if (variant.includes('search')) opts.searchEnabled = true;
        if (typeof defaults.searchEnabled === 'boolean') opts.searchEnabled = defaults.searchEnabled;
    }
    if (provider === 'pplx-web') {
        opts.model = variant; // Perplexity uses specific model names
        if (defaults.searchFocus) opts.searchFocus = defaults.searchFocus;
        if (Array.isArray(defaults.searchSources)) opts.searchSources = defaults.searchSources;
        if (typeof defaults.searchEnabled === 'boolean') opts.searchEnabled = defaults.searchEnabled;
    }
    if (provider === 'gemini-web') {
        opts.model = variant; // Gemini model variant key
    }
    if (provider === 'claude-web') {
        if (defaults.style_key) opts.style_key = defaults.style_key;
    }
    return opts;
}

connectWS();

