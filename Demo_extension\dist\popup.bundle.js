(()=>{const{GeminiWebModel:e,<PERSON><PERSON>ebModel:t,ClaudeWebModel:n,PerplexityWebModel:a,DeepseekWebModel:o,AIModelError:s,ErrorCode:i,configureAuth:l}=window.AIModelsBridge,d=document.getElementById("model-select"),r=document.getElementById("api-key-container");let c=document.getElementById("api-key");const m=document.getElementById("prompt"),u=document.getElementById("send-button"),g=document.getElementById("response"),p=document.getElementById("chat-container"),h=document.getElementById("load-conversations"),y=document.getElementById("conversations-list"),v=document.getElementById("new-thread-btn"),f=document.getElementById("image-preview"),E=document.createElement("div"),b=document.createElement("div"),C=document.getElementById("claude-styles-container"),T=document.getElementById("claude-style-select"),w=document.getElementById("edit-title-btn"),x=document.createElement("button"),L=document.getElementById("thread-title-container"),k=document.createElement("button"),M=document.createElement("button"),S=document.createElement("button"),I=document.getElementById("perplexity-options-container"),N=document.getElementById("perplexity-model-select"),H=(document.getElementById("perplexity-focus-select"),document.getElementById("perplexity-sources-container"),document.getElementById("perplexity-sources-checkboxes")),B=document.createElement("div"),$=document.getElementById("file-upload-btn");function D(){const e=d.value;"gemini-web"===e||"bing-web"===e?$.setAttribute("accept","image/*"):$.removeAttribute("accept")}d.addEventListener("change",D),window.addEventListener("DOMContentLoaded",D),x.id="share-conversation-btn",x.className="icon-btn",x.title="Share conversation",x.innerHTML="🔗",x.style.display="none",k.id="delete-conversation-btn",k.className="icon-btn",k.title="Delete conversation",k.innerHTML="🗑️",k.style.display="none",M.className="icon-btn",M.id="perplexity-private-btn",M.title="Make conversation private",M.innerHTML="🔒",M.style.display="none",S.id="get-convo-data-btn",S.className="icon-btn",S.title="Get Conversation Data (Log to Console)",S.innerHTML="📊",S.style.display="none",L&&(L.appendChild(x),L.appendChild(M),L.appendChild(k),L.appendChild(S)),B.id="gemini-options-container",B.className="hidden",B.innerHTML='\n  <div class="mode-toggle-container">\n    <label class="mode-toggle">\n      <span>Model:</span>\n      <select id="gemini-model-select" style="margin-left: 6px;">\n        <option value="gemini-2.5-flash" selected>Gemini 2.5 Flash</option>\n        <option value="gemini-2.5-pro">Gemini 2.5 Pro</option>\n      </select>\n    </label>\n  </div>\n',E.id="bing-mode-toggle",E.className="hidden",E.innerHTML='\n  <div class="mode-toggle-container">\n    <label class="mode-toggle">\n      <span>Mode:</span>\n      <select id="bing-mode-select">\n        <option value="chat" selected>Chat</option>\n        <option value="reasoning">Reasoning</option>\n        <option value="smart">Smart</option>\n        <option value="research">Research</option>\n      </select>\n      <span class="mode-info" title="Chat is conversational, Reasoning is more analytical">ⓘ</span>\n    </label>\n  </div>\n',b.id="deepseek-toggles",b.className="hidden",b.innerHTML='\n  <div class="deepseek-toggle-row" style="display: flex; gap: 16px; justify-content: space-between;">\n    <div class="mode-toggle-container" style="flex:1;">\n      <label class="mode-toggle" style="width:100%;">\n        <span>Search:</span>\n        <select id="deepseek-search-select" style="margin-left: 6px;">\n          <option value="enabled" selected>Enabled</option>\n          <option value="disabled">Disabled</option>\n        </select>\n        <span class="mode-info" title="Enable or disable Deepseek\'s internet search features">ⓘ</span>\n      </label>\n    </div>\n    <div class="mode-toggle-container" style="flex:1; text-align:right;">\n      <label class="mode-toggle" style="width:100%;">\n        <span>Mode:</span>\n        <select id="deepseek-mode-select" style="margin-left: 6px;">\n          <option value="chat" selected>Chat</option>\n          <option value="reasoning">Reasoning</option>\n        </select>\n        <span class="mode-info" title="Chat is conversational, Reasoning is more analytical">ⓘ</span>\n      </label>\n    </div>\n  </div>\n',document.addEventListener("change",e=>{e.target&&"deepseek-mode-select"===e.target.id&&chrome.storage.local.set({deepseekMode:e.target.value}),e.target&&"deepseek-search-select"===e.target.id&&chrome.storage.local.set({deepseekSearch:e.target.value})});const A=document.querySelector(".input-container");if(A)A.insertBefore(E,document.querySelector(".button-container")),A.insertBefore(b,document.querySelector(".button-container")),A.insertBefore(B,document.querySelector(".button-container"));else{const e=m.parentElement;e&&(e.insertBefore(E,m),e.insertBefore(b,m),e.insertBefore(B,m))}let R=null,W=[],j=null,F=null,U=null,G=null,P="internet",O=["web"];function Z(e,t="info",n=3e3){const a=function(){let e=document.getElementById("toast-container");return e||(e=document.createElement("div"),e.id="toast-container",e.style.position="fixed",e.style.bottom="20px",e.style.right="20px",e.style.zIndex="1000",document.body.appendChild(e)),e}(),o=document.createElement("div");return o.className=`toast toast-${t}`,o.innerHTML=e,o.style.backgroundColor="info"===t?"#3498db":"#e74c3c",o.style.color="white",o.style.padding="10px 15px",o.style.borderRadius="4px",o.style.marginTop="10px",o.style.boxShadow="0 2px 5px rgba(0,0,0,0.2)",o.style.transition="all 0.3s ease",o.style.opacity="0",a.appendChild(o),setTimeout(()=>{o.style.opacity="1"},10),setTimeout(()=>{o.style.opacity="0",setTimeout(()=>{a.removeChild(o)},300)},n),o}function _(t){const n=document.getElementById("current-thread-title");if(n){let a=t||"New Conversation",o="";R instanceof e&&R.currentThread?.metadata?.emoji&&(o=R.currentThread.metadata.emoji,a.startsWith(o+" ")||(a=`${o} ${a}`)),n.textContent=a,n.style.display=t&&j?"":"none"}}async function q(){const e=d.value;await chrome.storage.local.set({selectedModel:e}),j=null,await chrome.storage.local.remove(["currentThreadId"]),R=oe(),y.classList.add("hidden"),C.classList.add("hidden"),E.classList.add("hidden"),b.classList.add("hidden"),B.classList.add("hidden"),T.innerHTML="",I.classList.add("hidden"),"claude-web"===e?(r.classList.add("hidden"),C.classList.remove("hidden"),R&&Q()):"bing-web"===e?(r.classList.add("hidden"),E.classList.remove("hidden")):"gemini-web"===e?(r.classList.add("hidden"),B.classList.remove("hidden")):"perplexity-web"===e?(r.classList.add("hidden"),I.classList.remove("hidden"),R&&me()):"deepseek-web"===e&&(r.classList.add("hidden"),b.classList.remove("hidden")),le(),_(null),K()}function K(){console.log("Updating button visibility. Model:",R?.getName(),"Thread ID:",j),w.style.display="none",x.style.display="none",k.style.display="none",M.style.display="none",S.style.display="none";const t=!!j,s=R?.currentThread?.messages&&R.currentThread.messages.length>0,i=R?.currentThread?.messages&&R.currentThread.messages.length>=2;console.log(`isThreadLoaded: ${t}, hasMessages: ${s}, hasMultipleMessages: ${i}`),t&&(R instanceof n?(w.style.display=s?"":"none",x.style.display=i?"":"none",k.style.display=s?"":"none",S.style.display=s?"":"none"):R instanceof a?(w.style.display=s?"":"none",x.style.display=i?"":"none",k.style.display=s?"":"none",M.style.display=i?"":"none"):R instanceof e?(w.style.display=s?"":"none",x.style.display=i?"":"none",M.style.display=i?"":"none",k.style.display=s?"":"none",S.style.display=s?"":"none"):R instanceof o&&(k.style.display=s?"":"none",S.style.display=s?"":"none",w.style.display=s?"":"none",x.style.display="none",M.style.display="none")),console.log("Button Visibility:",{edit:w.style.display,share:x.style.display,delete:k.style.display,unshare:M.style.display,getData:S.style.display})}function V(){if(!R||!j)return void Z("No conversation selected to rename","error");const t=document.getElementById("current-thread-title");let n=t.textContent||"New Conversation";if(R instanceof e&&R.currentThread?.metadata?.emoji){const e=R.currentThread.metadata.emoji;n=n.replace(`${e} `,"")}t.style.display="none",w.style.display="none",x.style.display="none",k.style.display="none",M.style.display="none",S.style.display="none";const a=document.getElementById("thread-title-container"),o=document.createElement("div");o.className="title-edit-container";let s=`\n    <input type="text" id="title-input" value="${n}" placeholder="Enter conversation title" style="flex-grow: 1; margin-right: 5px;">\n  `;R instanceof e&&(s+=`\n      <input type="text" id="emoji-input" value="${R.currentThread?.metadata?.emoji||""}" placeholder="Emoji" maxlength="2" style="width: 60px; margin-right: 5px; text-align: center;">\n    `),s+='\n    <button id="save-title-btn" class="btn">Save</button>\n    <button id="cancel-title-btn" class="btn">Cancel</button>\n  ',o.innerHTML=s,o.style.display="flex",o.style.alignItems="center",a.appendChild(o);const i=document.getElementById("title-input");i.focus(),i.select(),document.getElementById("save-title-btn").addEventListener("click",z),document.getElementById("cancel-title-btn").addEventListener("click",Y),i.addEventListener("keyup",e=>{"Enter"===e.key?z():"Escape"===e.key&&Y()})}function Y(){const e=document.getElementById("thread-title-container"),t=e.querySelector(".title-edit-container");t&&e.removeChild(t),document.getElementById("current-thread-title").style.display="",w.style.display="",K()}async function z(){const t=document.getElementById("title-input").value.trim();if(!t)return void Z("Title cannot be empty","error");const s=document.getElementById("save-title-btn"),i=s.textContent;s.textContent="Saving...",s.disabled=!0;try{if(R instanceof n||R instanceof a||R instanceof o)await R.editTitle(t);else{if(!(R instanceof e))return Z("Title editing is not supported for this model.","error"),s.textContent=i,s.disabled=!1,void Y();{const e=document.getElementById("emoji-input"),n=e?e.value.trim():void 0;await R.editTitle(t,n)}}_(t),R.currentThread&&(R.currentThread.title=t),Z("Title updated successfully","info"),Y()}catch(e){console.error("Error updating title:",e),Z(`Failed to update title: ${se(e)}`,"error"),s.textContent=i,s.disabled=!1}}async function J(e=!0){try{console.log("Creating new thread...",e),y.classList.add("hidden"),console.log("eeeeeeeeeeeee:",R),R||(R=oe()),await R.initNewThread(),console.log("New thread created:",R),R.currentThread?(j=R instanceof o&&R.currentThread.metadata?.conversationId||R.currentThread.id,await chrome.storage.local.set({currentThreadId:j}),_(null)):_(null),le(),K(),e&&ie()}catch(e){console.error("Error creating thread:",e);const t=document.createElement("div");t.className="message assistant-message",t.textContent=`Error creating new thread: ${e.message}`,p.appendChild(t)}}async function Q(){try{if(!(R&&R instanceof n))return;const e=await R.getStyles();if(!e)return void console.error("Failed to get Claude styles");if(F=e,e.defaultStyles&&Array.isArray(e.defaultStyles)){const t=document.createElement("option");t.disabled=!0,t.textContent="───Styles───",T.appendChild(t),e.defaultStyles.forEach(e=>{const t=document.createElement("option");t.value=e.key,t.textContent=e.name,T.appendChild(t)})}if(e.customStyles&&Array.isArray(e.customStyles)){if(e.defaultStyles&&e.defaultStyles.length>0){const e=document.createElement("option");e.disabled=!0,e.textContent="──────────",T.appendChild(e)}e.customStyles.forEach(e=>{const t=document.createElement("option");t.value=e.key,t.textContent=e.name,T.appendChild(t)})}chrome.storage.local.get(["claudeStyleKey"],e=>{e.claudeStyleKey&&Array.from(T.options).some(t=>t.value===e.claudeStyleKey)&&(T.value=e.claudeStyleKey,U=e.claudeStyleKey)})}catch(e){console.error("Error loading Claude styles:",e)}}function X(){if(!R||!R.currentThread)return;le(),_(R.currentThread.title);const e=R.currentThread.messages;K(),e&&e.length>0?(e.forEach((e,t)=>{const n=document.createElement("div");if(n.className="message "+("user"===e.role?"user-message":"assistant-message"),n.dataset.messageIndex=t,"assistant"===e.role){if(e.reasoning&&e.reasoning.content&&e.reasoning.content.trim()){const a=document.createElement("div");a.id=`reasoning-${e.id||t}`,a.className="message-reasoning-container collapsed";const o=e.reasoning.elapsedSecs??e.metadata?.reasoningTimeSecs,s=o?`Thought for ${o} second${1===o?"":"s"}`:"Thinking Process";a.innerHTML=`\n<div class="message-reasoning-header">\n<span class="thought-icon">🧠</span>\n<span>${s}</span>\n<span class="toggle-icon">▼</span>\n</div>\n<div class="message-reasoning-content">${e.reasoning.content.trim()}</div>\n`,n.appendChild(a)}const a=document.createElement("div");a.className="message-text-content",a.innerHTML=e.content,n.appendChild(a)}else if("user"===e.role)if(e.metadata?.imageDataUrl||e.metadata?.imageUrl){const t=e.metadata.imageDataUrl||e.metadata.imageUrl;n.innerHTML=`\n              <div class="message-image-container">\n                <img src="${t}" alt="User uploaded image" class="message-image" onerror="this.onerror=null; this.src='data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjEyMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMjAwIiBoZWlnaHQ9IjEyMCIgZmlsbD0iI2VlZSIvPjx0ZXh0IHg9IjUwJSIgeT0iNTAlIiBmb250LWZhbWlseT0iQXJpYWwsIHNhbnMtc2VyaWYiIGZvbnQtc2l6ZT0iMTQiIHRleHQtYW5jaG9yPSJtaWRkbGUiIGFsaWdubWVudC1iYXNlbGluZT0ibWlkZGxlIiBmaWxsPSIjOTk5Ij5JbWFnZSB1bmF2YWlsYWJsZTwvdGV4dD48L3N2Zz4=';">\n              </div>\n              <div class="message-text-content">${e.content}</div>\n            `}else{const t=document.createElement("div");t.className="message-text-content",t.textContent=e.content,n.appendChild(t)}else{const t=document.createElement("div");t.className="message-text-content",t.textContent=e.content,n.appendChild(t)}if(p.appendChild(n),"assistant"===e.role&&e.metadata?.suggestedResponses){const t=document.createElement("div");t.className="suggestions-container",e.metadata.suggestedResponses.forEach(e=>{const n=document.createElement("button");n.className="suggestion-btn",n.textContent=e,n.addEventListener("click",()=>{m.value=e,ne()}),t.appendChild(n)}),p.appendChild(t)}}),g.scrollTop=g.scrollHeight):ie()}function ee(){if(f.innerHTML="",0===W.length)return $.value="",void f.classList.add("hidden");f.classList.remove("hidden"),W.forEach((e,t)=>{const n=document.createElement("div");n.className="image-preview-item";let a="";if(e.type.startsWith("image/")){const n=new FileReader;n.onload=function(n){a=`\n        <img src="${n.target.result}" alt="Selected image ${t+1}" class="preview-image">\n        <div class="file-preview-name">${e.name}</div>\n        `,o()},n.readAsDataURL(e)}else a=`\n        <div class="file-preview-icon">📄</div>\n        <div class="file-preview-name">${e.name}</div>\n      `,o();function o(){n.innerHTML=`\n        ${a}\n        <button class="remove-btn" data-index="${t}" title="Remove file">&times;</button>\n      `,n.querySelector(".remove-btn").onclick=()=>{W.splice(t,1),ee()}}f.appendChild(n)})}function te(e){const t=e.target.files;if(!t||0===t.length)return;let n=W.length;const a=Array.from(t);if(n+a.length>4)return Z("Maximum of 4 files allowed.","error"),void($.value="");const o=d.value,s="gemini-web"===o||"bing-web"===o;if(a.forEach(e=>{!s||e.type.startsWith("image/")?W.push(e):Z(`File "${e.name}" is not an image.`,"error")}),ee(),"deepseek-web"===o){const e=document.getElementById("deepseek-search-select");W.length>0&&e?(e.disabled=!0,Z("Attachments are enabled – disabling search.","info")):e&&(e.disabled=!1)}const i=document.getElementById("deepseek-search-select");i&&i.addEventListener("change",e=>{"enabled"===i.value&&W.length>0&&(Z("You can't enable search when attachments are added. Clearing attachments.","error"),W=[],ee(),$.value="")})}async function ne(){const t=m.value.trim();if(t)try{u.disabled=!0,u.innerHTML='<span class="loading-spinner"></span>Sending...',R||(R=oe()),j||await J(!1);const n=document.createElement("div");n.className="message user-message";let s=`<div class="message-text">${m.value}</div>`;if(W.length>0){let e='<div class="message-image-container">';W.forEach((t,n)=>{const a=URL.createObjectURL(t);e+=`<img src="${a}" alt="User uploaded image ${n+1}" class="message-image">`}),e+="</div>",s=e+s}n.innerHTML=s,p.appendChild(n),p.scrollTop=p.scrollHeight;const i=document.createElement("div");i.className="message assistant-message",i.innerHTML='<div class="message-text-content"><div class="loading">Thinking...</div></div>',p.appendChild(i),p.scrollTop=p.scrollHeight,m.value="",f.innerHTML="",f.classList.add("hidden");const l=[...W];let r;if(W=[],$.value="",console.log("Sending message to model:",t),console.log("Current model type:",R.getName()),"bing-web"===d.value&&!E.classList.contains("hidden")){const e=document.getElementById("bing-mode-select");e&&(r=e.value,console.log("Using Bing mode:",r))}console.log(U);const c={images:l,signal:null,mode:r,style_key:U,model:void 0,searchFocus:void 0,searchSources:void 0,onEvent:e=>{const t=document.querySelector(".message.assistant-message:last-of-type");switch(e.type){case"UPDATE_ANSWER":console.log("Assistant message update:",e.data),t&&t.classList.contains("hidden")&&t.classList.remove("hidden");const n=t?.querySelector(".message-text-content");if(n?n.innerHTML=e.data.text||'<div class="loading">Thinking...</div>':t&&(console.warn("Could not find .message-text-content to update."),t.innerHTML=e.data.text||'<div class="loading">Thinking...</div>'),g.scrollTop=g.scrollHeight,e.data.reasoning&&void 0!==e.data.reasoning.content&&t){let n=t.querySelector(".message-reasoning-container"),a=e.data.reasoning.elapsedSecs;const o=a?`Thought for ${a} second${1===a?"":"s"}`:"Reasoning...";if(!n&&e.data.reasoning.content.trim())n=document.createElement("div"),n.className="message-reasoning-container collapsed",n.innerHTML=`\n<div class="message-reasoning-header">\n<span class="thought-icon">🧠</span>\n<span>${o}</span>\n<span class="toggle-icon">▼</span>\n</div>\n<div class="message-reasoning-content">${e.data.reasoning.content.trim()}</div>\n`,t.prepend(n);else if(n){const t=n.querySelector(".message-reasoning-content");t&&(t.innerHTML=e.data.reasoning.content.trim());const s=n.querySelector(".message-reasoning-header span:nth-child(2)");s&&(s.textContent=o),a&&n.classList.remove("collapsed"),n.scrollTop=n.scrollHeight}}p.scrollTop=p.scrollHeight,g.scrollTop=g.scrollHeight;break;case"ERROR":const a=document.querySelector(".message.assistant-message:last-of-type");a&&!a.classList.contains("hidden")&&a.classList.add("hidden");const o=document.createElement("div");o.className="message error-message",o.textContent=`Error: ${e.error.message}`,p?(p.appendChild(o),p.scrollTop=p.scrollHeight):console.error("Chat container not found during error handling."),u?(u.disabled=!1,u.textContent="Send"):console.error("Send button not found during error handling.");break;case"DONE":u.disabled=!1,u.textContent="Send",W=[],K();break;case"TITLE_UPDATE":_(e.data.title);break;case"SUGGESTED_RESPONSES":e.data.suggestions&&e.data.suggestions.length>0&&function(e){if(!e||!e.length)return;console.log("Displaying suggested responses:",e);const t=document.createElement("div");t.className="suggestions-container",e.forEach(e=>{const n=document.createElement("button");n.className="suggestion-btn",n.textContent=e,n.addEventListener("click",()=>{m.value=e,ne()}),t.appendChild(n)}),p.appendChild(t)}(e.data.suggestions)}}};if(R instanceof a&&(c.model=G||R.defaultModel,P=O.length>0?"internet":"writing",c.searchFocus=P,c.searchSources=O.length>0?O:void 0,console.log("Sending Perplexity Options:",{model:c.model,searchFocus:c.searchFocus,searchSources:c.searchSources})),R instanceof o){let e="chat";const t=document.getElementById("deepseek-mode-select");t&&(e=t.value);let n=!1;const a=document.getElementById("deepseek-search-select");a&&(n="enabled"===a.value),c.mode=e,c.searchEnabled=n}if(R instanceof e){const e=document.getElementById("gemini-model-select");e&&(c.model=e.value)}await R.sendMessage(t,c);const h=document.getElementById("deepseek-search-select");h&&(h.disabled=!1)}catch(e){console.error("Error sending message:",e);const t=document.createElement("div");t.className="message error-message",t.textContent=`Error: ${e.message||"Unknown error occurred"}`,p.appendChild(t),p.scrollTop=p.scrollHeight,u.disabled=!1,u.textContent="Send"}}async function ae(){try{y.classList.add("hidden"),R||(R=oe());const e=(await R.getAllThreads()).filter(e=>e.modelName===R.getName());if(y.innerHTML="",y.classList.remove("hidden"),0===e.length){const e=document.createElement("div");return e.className="no-threads-msg",e.textContent="No conversations found",void y.appendChild(e)}e.sort((e,t)=>t.updatedAt-e.updatedAt);const t=document.createElement("div");t.className="threads-header",t.textContent="Your Conversations",y.appendChild(t);const n=document.createElement("div");n.className="threads-container",y.appendChild(n),e.forEach(e=>{const t=document.createElement("div");t.className="thread-item",j===e.id&&t.classList.add("active-thread"),t.dataset.threadId=e.id;const a=document.createElement("div");a.className="thread-title",a.textContent=e.title||`Conversation ${new Date(e.createdAt).toLocaleString()}`,t.appendChild(a);const o=document.createElement("div");o.className="thread-date",o.textContent=`Last updated: ${function(e){const t=new Date,n=new Date(e),a=Math.floor((t-n)/1e3),o=Math.floor(a/60),s=Math.floor(o/60),i=Math.floor(s/24);if(a<60)return"just now";if(o<60)return`${o} ${1===o?"minute":"minutes"} ago`;if(s<5)return`${s} ${1===s?"hour":"hours"} ago`;const l=n.toLocaleTimeString(void 0,{hour:"2-digit",minute:"2-digit"});if(t.toDateString()===n.toDateString())return`today, at ${l}`;const d=new Date(t);if(d.setDate(t.getDate()-1),d.toDateString()===n.toDateString())return`yesterday, at ${l}`;const r=new Date(t);return r.setDate(t.getDate()-2),r.toDateString()===n.toDateString()?`day before yesterday, at ${l}`:i<7?`${n.toLocaleDateString(void 0,{weekday:"long"})}, at ${l}`:`${n.toLocaleDateString(void 0,{weekday:"long",day:"numeric",month:"long"})}, at ${l}`}(e.updatedAt)}`,t.appendChild(o),t.addEventListener("click",async()=>{await async function(e){try{R||(R=oe()),await R.loadThread(e),j=e,await chrome.storage.local.set({currentThreadId:j}),X();const t=document.getElementById("current-thread-title");t&&R.currentThread?.title&&(t.textContent=R.currentThread.title),console.log("Thread loaded successfully:",e)}catch(e){console.error("Error loading thread:",e),alert("Failed to load conversation: "+se(e))}}(e.id),y.classList.add("hidden")}),n.appendChild(t)})}catch(e){console.error("Error loading threads:",e),alert("Failed to load conversations: "+se(e))}}function oe(){const s=d.value;switch(console.log(s),s){case"gemini-web":return new e;case"bing-web":return new t;case"claude-web":const s=new n;return setTimeout(async()=>await Q(),500),s;case"perplexity-web":const i=new a;return setTimeout(async()=>await me(),0),i;case"deepseek-web":return new o;default:throw new Error("Unknown model selected")}}function se(e){return e instanceof s||e instanceof Error?e.message:String(e)}function ie(){const e=document.createElement("div");e.className="welcome-message";const t=["What would you like to know today?","What's on your mind today?","Ask me anything...","How can I assist you today?","Ready when you are! Type a message to begin.","What are you curious about?","What would you like to explore today?"],n=t[Math.floor(Math.random()*t.length)];e.textContent=n,p.appendChild(e)}function le(){p.innerHTML=""}async function de(){if(R&&j)if(R instanceof n||R instanceof a||R instanceof e)try{const e=x.innerHTML;x.innerHTML="⏳",x.disabled=!0,!function(e){const t=document.createElement("div");t.className="modal-container",t.style.position="fixed",t.style.top="0",t.style.left="0",t.style.width="100%",t.style.height="100%",t.style.backgroundColor="rgba(0, 0, 0, 0.5)",t.style.display="flex",t.style.justifyContent="center",t.style.alignItems="center",t.style.zIndex="1000";const n=document.createElement("div");n.className="modal-content",n.style.backgroundColor="white",n.style.padding="20px",n.style.borderRadius="8px",n.style.maxWidth="90%",n.style.width="400px",n.style.boxShadow="0 4px 8px rgba(0, 0, 0, 0.2)";const a=document.createElement("div");a.className="modal-header",a.style.display="flex",a.style.justifyContent="space-between",a.style.alignItems="center",a.style.marginBottom="15px";const o=document.createElement("h3");o.textContent="Conversation Shared!",o.style.margin="0";const s=document.createElement("button");s.innerHTML="×",s.style.background="none",s.style.border="none",s.style.fontSize="24px",s.style.cursor="pointer",s.style.padding="0 5px",s.onclick=()=>document.body.removeChild(t),a.appendChild(o),a.appendChild(s);const i=document.createElement("div");i.className="modal-body";const l=document.createElement("p");l.textContent="Your conversation has been shared. Anyone with this link can view it:";const d=document.createElement("div");d.style.display="flex",d.style.marginBottom="15px",d.style.marginTop="15px";const r=document.createElement("input");r.type="text",r.value=e,r.readOnly=!0,r.style.flexGrow="1",r.style.padding="8px",r.style.border="1px solid #ccc",r.style.borderRadius="4px 0 0 4px";const c=document.createElement("button");c.textContent="Copy",c.className="btn",c.style.borderRadius="0 4px 4px 0",c.style.margin="0",c.onclick=()=>{r.select(),document.execCommand("copy"),c.textContent="Copied!",setTimeout(()=>{c.textContent="Copy"},2e3)},d.appendChild(r),d.appendChild(c);const m=document.createElement("button");m.textContent="Open in Browser",m.className="btn",m.style.width="100%",m.style.marginTop="10px",m.onclick=()=>{window.open(e,"_blank")},i.appendChild(l),i.appendChild(d),i.appendChild(m),n.appendChild(a),n.appendChild(i),t.appendChild(n),document.body.appendChild(t)}(await R.shareConversation()),x.innerHTML=e,x.disabled=!1}catch(e){console.error("Error sharing conversation:",e),Z(`Failed to share conversation: ${se(e)}`,"error"),x.innerHTML="🔗",x.disabled=!1}else Z("Sharing is only supported for Claude, Gemini and Perplexity conversations","error");else Z("No conversation selected to share","error")}function re(){if(!R||!j)return void Z("No conversation selected to delete","error");let t="Are you sure you want to delete this conversation (this will only remove it locally)? This action cannot be undone.";R instanceof n||R instanceof a||R instanceof e||(t="Are you sure you want to delete this conversation? This action cannot be undone. This will remove it locally and from the AI model servers.");const o=document.createElement("div");o.className="modal-container";const s=document.createElement("div");s.className="modal-content",s.style.maxWidth="350px";const i=document.createElement("div");i.className="modal-header";const l=document.createElement("h3");l.textContent="Delete Conversation",l.style.color="#d32f2f";const d=document.createElement("button");d.innerHTML="×",d.style.background="none",d.style.border="none",d.style.fontSize="24px",d.style.cursor="pointer",d.style.padding="0 5px",d.onclick=()=>document.body.removeChild(o),i.appendChild(l),i.appendChild(d);const r=document.createElement("div");r.className="modal-body";const c=document.createElement("p");c.textContent=t;const m=document.createElement("div");m.style.display="flex",m.style.justifyContent="flex-end",m.style.marginTop="20px",m.style.gap="10px";const u=document.createElement("button");u.textContent="Cancel",u.className="btn",u.style.backgroundColor="#9e9e9e",u.onclick=()=>document.body.removeChild(o);const g=document.createElement("button");g.textContent="Delete",g.className="btn",g.style.backgroundColor="#d32f2f",g.onclick=()=>{document.body.removeChild(o),async function(){try{const e=k.innerHTML;if(k.innerHTML="⏳",k.disabled=!0,!R.currentThread||!R.currentThread.metadata)throw new Error("No thread metadata available");"function"==typeof R.deleteServerThreads?(await R.deleteServerThreads([R.currentThread.metadata.conversationId],!0,!1),Z("Conversation deleted successfully (Server & Local)","info")):(await R.deleteThread(j,!1),Z("Conversation deleted successfully (Local Only)","info")),j=null,await chrome.storage.local.remove(["currentThreadId"]),await J(!1),R.currentThread&&(j=R.currentThread.id,await chrome.storage.local.set({currentThreadId:j}),_(R.currentThread.title)),k.innerHTML=e,k.disabled=!1}catch(e){console.error("Error deleting conversation:",e),Z(`Failed to delete conversation: ${se(e)}`,"error"),k.innerHTML="🗑️",k.disabled=!1}}()},m.appendChild(u),m.appendChild(g),r.appendChild(c),r.appendChild(m),s.appendChild(i),s.appendChild(r),o.appendChild(s),document.body.appendChild(o)}async function ce(){if(R&&j)if(R instanceof a||R instanceof e)try{const e=M.innerHTML;M.innerHTML="⏳",M.disabled=!0,await R.unShareConversation()?Z("Conversation set to private","info"):Z("Failed to set conversation to private","error"),M.innerHTML=e,M.disabled=!1}catch(e){console.error("Error setting conversation to private:",e),Z(`Failed to set conversation to private: ${se(e)}`,"error"),M.innerHTML="🔒",M.disabled=!1}else Z("This operation is only supported for Perplexity and Gemini conversations","error");else Z("No conversation selected","error")}function me(){if(R&&R instanceof a)try{const e=R.getModels();N.innerHTML="";for(const t in e){const e=document.createElement("option");e.value=t,e.textContent=t,N.appendChild(e)}N.value=G||R.defaultModel,G=N.value;const t=R.getSearchSources();H.innerHTML="";const n=document.createElement("div");n.className="sources-wrapper",H.appendChild(n),t.forEach(e=>{const t=document.createElement("label");t.className="toggle-switch-label",O.includes(e)&&t.classList.add("active");const a=document.createElement("input");a.type="checkbox",a.value=e,a.id=`source-${e}`,a.checked=O.includes(e),t.textContent=e.charAt(0).toUpperCase()+e.slice(1),t.appendChild(a),n.appendChild(t)}),P=O.length>0?"internet":"writing"}catch(e){console.error("Error loading Perplexity options:",e),Z("Failed to load Perplexity options","error")}}async function ue(){if(!(R instanceof e||R instanceof n||R instanceof o))return void Z("Model not supported for getting conversation data.","error");if(!R||!j)return void Z("Please select a conversation first.","error");const t=S.innerHTML;S.innerHTML="⏳",S.disabled=!0;try{let e;console.log(`Fetching conversation data for thread: ${j} using model: ${R.getName()}`),R instanceof o?(e=await R.getAllConversationsData(),console.log("Deepseek All Conversations Data Received:",e)):(e=await R.getConversationData(),console.log("Conversation Data Received:",e)),console.log("Conversation Data Received:",e),Z("Conversation data logged to console.","info"),S.innerHTML=t,S.disabled=!1}catch(e){console.error("Error getting conversation data:",e),Z(`Failed to get conversation data: ${se(e)}`,"error"),S.innerHTML="📊",S.disabled=!1}}l({notifyTokenRefresh:!0}),document.addEventListener("DOMContentLoaded",async function(){const e=await chrome.storage.local.get(["apiKey","selectedModel","currentThreadId"]);e.apiKey&&(c.value=e.apiKey),e.selectedModel&&(d.value=e.selectedModel);const{deepseekMode:t,deepseekSearch:n}=await chrome.storage.local.get(["deepseekMode","deepseekSearch"]),a=document.getElementById("deepseek-mode-select");a&&t&&(a.value=t);const o=document.getElementById("deepseek-search-select");o&&n&&(o.value=n),await q(),T.addEventListener("change",async()=>{U=T.value,await chrome.storage.local.set({claudeStyleKey:U})}),N.addEventListener("change",()=>{G=N.value}),H.addEventListener("click",e=>{const t=e.target.closest(".toggle-switch-label");if(t){const e=t.querySelector('input[type="checkbox"]');if(e){const n=e.value;e.checked=!e.checked,e.checked?(O.includes(n)||O.push(n),t.classList.add("active")):(O=O.filter(e=>e!==n),t.classList.remove("active")),P=O.length>0?"internet":"writing"}}}),c?.addEventListener("change",async()=>{await chrome.storage.local.set({apiKey:c.value})}),d.addEventListener("change",q),$.addEventListener("change",te),u.addEventListener("click",ne),p.addEventListener("click",function(e){const t=e.target.closest(".message-reasoning-header");if(t){const e=t.closest(".message-reasoning-container");e&&e.classList.toggle("collapsed")}}),h.addEventListener("click",ae),v.addEventListener("click",J),w.addEventListener("click",V),x.addEventListener("click",de),k.addEventListener("click",re),M.addEventListener("click",ce),S.addEventListener("click",ue),document.addEventListener(window.AIModelsBridge.AUTH_EVENTS.TOKEN_REFRESH_START,e=>{e.detail.background||Z("Refreshing auth token...","info")}),document.addEventListener(window.AIModelsBridge.AUTH_EVENTS.TOKEN_REFRESH_COMPLETE,e=>{e.detail.background||Z("Auth token refreshed!","info",2e3)}),document.addEventListener(window.AIModelsBridge.AUTH_EVENTS.TOKEN_REFRESH_ERROR,e=>{Z(`Auth error: ${e.detail.error}`,"error",5e3)}),e.currentThreadId?(j=e.currentThreadId,await async function(){try{R||(R=oe()),j?(await R.loadThread(j),X()):await J()}catch(e){console.error("Error initializing model or loading thread:",e),p.innerHTML=`<div class="message error-message">Error: ${e.message}</div>`}}()):await ae(),D()})})();