{"manifest_version": 3, "name": "WebAI Extension", "version": "0.1.0", "description": "Expose web AI models via a local OpenAI-compatible server.", "permissions": ["storage", "tabs", "scripting", "cookies"], "host_permissions": ["https://gemini.google.com/*", "https://claude.ai/*", "https://perplexity.ai/*", "https://www.perplexity.ai/*", "https://copilot.microsoft.com/*", "https://chat.deepseek.com/*"], "background": {"service_worker": "background.js", "type": "module"}, "options_page": "options.html", "action": {"default_title": "WebAI"}, "web_accessible_resources": [{"resources": ["lib/ai-models-bridge.min.js", "lib/ai-models-bridge.esm.js", "lib/index.js", "lib/utils/*", "lib/models/*"], "matches": ["<all_urls>"]}]}