// Test script for advanced WebAI features
// Run this in the browser console after loading the extension

async function testAdvancedFeatures() {
    console.log('🧪 Testing Advanced WebAI Features...');

    const baseUrl = 'http://localhost:11434';

    // Test 1: Get API key
    console.log('\n1️⃣ Testing API Key retrieval...');
    try {
        const keyResp = await fetch(`${baseUrl}/keys/current`);
        if (!keyResp.ok) {
            throw new Error('No API key available. Create one in Options first.');
        }
        const keyData = await keyResp.json();
        const apiKey = keyData.api_key;
        console.log('✅ API Key retrieved:', apiKey.substring(0, 8) + '...');

        // Test 2: Smart Conversation Matching
        console.log('\n2️⃣ Testing Smart Conversation Matching...');

        // Test 1: New conversation with system message
        console.log('\n📝 Test 2a: New conversation with system message');
        const newConvResp = await fetch(`${baseUrl}/v1/chat/completions`, {
            method: 'POST',
            headers: {
                'Authorization': `Bearer ${apiKey}`,
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                model: 'gemini-web:2.0-flash',
                messages: [
                    { role: 'system', content: 'You are a helpful assistant specialized in testing. Always start your response with "TEST_SYSTEM_MSG:"' },
                    { role: 'user', content: 'Hello! This is a new conversation test.' }
                ],
                stream: false
            })
        });

        if (newConvResp.ok) {
            const data = await newConvResp.json();
            const response = data.choices?.[0]?.message?.content || '';
            if (response.includes('TEST_SYSTEM_MSG:')) {
                console.log('✅ System message properly combined with user message');
            } else {
                console.log('⚠️ System message may not have been combined properly');
            }
        } else {
            console.log('⚠️ New conversation failed:', await newConvResp.text());
        }

        // Test 2: Single user message (should create new thread)
        console.log('\n📝 Test 2b: Single user message');
        const singleMsgResp = await fetch(`${baseUrl}/v1/chat/completions`, {
            method: 'POST',
            headers: {
                'Authorization': `Bearer ${apiKey}`,
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                model: 'gemini-web:2.0-flash',
                messages: [{ role: 'user', content: 'What is 2+2?' }],
                stream: false
            })
        });

        if (singleMsgResp.ok) {
            console.log('✅ Single message conversation handled');
        } else {
            console.log('⚠️ Single message failed:', await singleMsgResp.text());
        }

        // Test 3: Continuing conversation (should match existing thread)
        console.log('\n📝 Test 2c: Continuing conversation simulation');

        // First, send the initial message
        const firstMsgResp = await fetch(`${baseUrl}/v1/chat/completions`, {
            method: 'POST',
            headers: {
                'Authorization': `Bearer ${apiKey}`,
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                model: 'gemini-web:2.0-flash',
                messages: [{ role: 'user', content: 'What is 2+2?' }],
                stream: false
            })
        });

        if (firstMsgResp.ok) {
            const firstData = await firstMsgResp.json();
            const assistantReply = firstData.choices?.[0]?.message?.content || 'Assistant response';
            console.log('✅ First message sent successfully');

            // Now send a follow-up that should match the existing conversation
            const followUpResp = await fetch(`${baseUrl}/v1/chat/completions`, {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${apiKey}`,
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    model: 'gemini-web:2.0-flash',
                    messages: [
                        { role: 'user', content: 'What is 2+2?' },
                        { role: 'assistant', content: assistantReply },
                        { role: 'user', content: 'What about 3+3?' }
                    ],
                    stream: false
                })
            });

            if (followUpResp.ok) {
                console.log('✅ Follow-up message should have matched existing thread');
            } else {
                console.log('⚠️ Follow-up message failed:', await followUpResp.text());
            }
        } else {
            console.log('⚠️ First message failed:', await firstMsgResp.text());
        }

        // List threads to see results
        const threadsResp = await fetch(`${baseUrl}/v1/threads`, {
            headers: { 'Authorization': `Bearer ${apiKey}` }
        });
        const threadsData = await threadsResp.json();
        console.log('✅ Total threads after tests:', threadsData.threads?.length || 0, 'conversations');

        // Test 3: Image Proxy
        console.log('\n3️⃣ Testing Image Proxy...');
        const imageUrl = 'https://httpbin.org/image/png';
        const proxyResp = await fetch(`${baseUrl}/proxy-image?url=${encodeURIComponent(imageUrl)}`);

        if (proxyResp.ok) {
            const contentType = proxyResp.headers.get('content-type');
            console.log('✅ Image proxy working, content-type:', contentType);
        } else {
            console.log('⚠️ Image proxy failed:', await proxyResp.text());
        }

        // Test 4: Provider Defaults
        console.log('\n4️⃣ Testing Provider Defaults...');
        const testDefaults = {
            'pplx-web': { searchFocus: 'internet', searchSources: ['web', 'scholar'] },
            'claude-web': { style_key: 'creative' },
            'deepseek-web': { searchEnabled: true }
        };

        // Store defaults
        await new Promise(resolve => {
            chrome.storage.local.set({ 'webai_provider_defaults': testDefaults }, resolve);
        });

        // Verify storage
        const stored = await new Promise(resolve => {
            chrome.storage.local.get(['webai_provider_defaults'], resolve);
        });

        if (stored.webai_provider_defaults) {
            console.log('✅ Provider defaults stored and retrieved successfully');
        } else {
            console.log('⚠️ Provider defaults storage failed');
        }

        console.log('\n🎉 Advanced features test completed!');
        console.log('📋 Summary:');
        console.log('- Smart Conversation Matching: Automatic thread detection and creation');
        console.log('- Thread Management: API endpoints for conversation handling');
        console.log('- Image Proxy: Server-side image fetching for restricted origins');
        console.log('- Provider Defaults: Immediate-apply configuration system');
        console.log('- Authentication Events: Token refresh monitoring');
        console.log('\n🧠 Conversation Matching Logic:');
        console.log('- New conversations (1-2 messages): Creates new thread');
        console.log('- System + User messages: Combines both into single prompt');
        console.log('- Longer conversations: Excludes last message, matches previous history');
        console.log('- Thread matching: Finds best subsequence match in existing threads');
        console.log('- No match found: Creates new thread with full context');
        console.log('\n⚡ Performance Optimizations:');
        console.log('- Only checks 20 most recent threads');
        console.log('- Limits message comparison to first 10 messages');
        console.log('- Fast content similarity for short messages');
        console.log('- Early exit for perfect matches (95%+ similarity)');

    } catch (error) {
        console.error('❌ Test failed:', error.message);
        console.log('💡 Make sure:');
        console.log('1. WebAI server is running on localhost:11434');
        console.log('2. Extension is loaded and connected');
        console.log('3. At least one API key exists (check Options page)');
    }
}

// Auto-run test
testAdvancedFeatures();
