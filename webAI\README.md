# WebAI Extension

A production-ready MV3 browser extension that integrates the AI-Models-Bridge library and bridges to the local WebAI server.

## Folders
- lib/: copy the compiled Library/dist files here (ai-models-bridge.min.js, ai-models-bridge.esm.js, index.js and subfolders)
- background.js: service worker that connects to the server and routes requests
- options.html/js/css: settings UI

## Setup
1. Build/copy library bundle
   - Copy the contents of `../Library/dist/*` into `webAI/lib/` so `ai-models-bridge.min.js` is available.
2. Load the extension (Developer Mode -> Load Unpacked -> select webAI folder)
3. Open Options to set WS URL (default ws://localhost:11434/ws)

## Permissions
Grant host permissions for providers when prompted: gemini.google.com, claude.ai, perplexity.ai, copilot.microsoft.com, chat.deepseek.com.

## Notes
- The extension background runs even when the popup is closed.
- The server discovers available models via the extension and exposes them at /v1/models.

