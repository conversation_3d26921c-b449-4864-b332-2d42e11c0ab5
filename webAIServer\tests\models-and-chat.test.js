import assert from 'node:assert/strict';
import test from 'node:test';
import { spawn } from 'node:child_process';
import { setTimeout as delay } from 'node:timers/promises';
import http from 'node:http';

function httpRequest({ method = 'GET', path = '/', headers = {}, body }) {
    return new Promise((resolve, reject) => {
        const req = http.request({ hostname: 'localhost', port: 11434, path, method, headers }, (res) => {
            let data = '';
            res.on('data', (chunk) => { data += chunk; });
            res.on('end', () => resolve({ status: res.statusCode, body: data }));
        });
        req.on('error', reject);
        if (body) req.write(body);
        req.end();
    });
}

async function createApiKey() {
    const res = await httpRequest({ method: 'POST', path: '/keys/new' });
    assert.equal(res.status, 200);
    const json = JSON.parse(res.body);
    assert.ok(json.api_key, 'api_key missing');
    return json.api_key;
}

await test('models endpoint returns list when extension connected or 503 otherwise', async (t) => {
    const key = await createApiKey();
    const res = await httpRequest({ method: 'GET', path: '/v1/models', headers: { Authorization: `Bearer ${key}` } });
    // Accept either connected or disconnected state
    assert.ok(res.status === 200 || res.status === 503, `Unexpected status: ${res.status}`);
});

await test('chat completions abort signals are forwarded', async (t) => {
    // This test serves as a smoke check: we start a stream, then close the connection.
    // Without an extension connected, server will 503 and we skip.
    const key = await createApiKey();
    const res = await httpRequest({ method: 'GET', path: '/v1/models', headers: { Authorization: `Bearer ${key}` } });
    if (res.status !== 200) {
        t.diagnostic('Extension not connected; skipping streaming abort smoke test.');
        return;
    }

    // Start a streaming request and close early
    await new Promise((resolve, reject) => {
        const req = http.request({
            hostname: 'localhost', port: 11434, path: '/v1/chat/completions', method: 'POST', headers: {
                Authorization: `Bearer ${key}`,
                'Content-Type': 'application/json'
            }
        }, (res) => {
            // Close quickly to trigger abort
            res.req.destroy();
            resolve();
        });
        req.on('error', () => resolve()); // ignore network errors here
        const body = JSON.stringify({ model: 'gemini-web:gemini-2.0-flash', messages: [{ role: 'user', content: 'hi' }], stream: true });
        req.write(body);
        req.end();
    });
});

