[{"id": 1, "priority": 1, "action": {"type": "modifyHeaders", "requestHeaders": [{"header": "User-Agent", "operation": "set", "value": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"}]}, "condition": {"urlFilter": "*://gemini.google.com/*", "resourceTypes": ["xmlhttprequest", "main_frame", "sub_frame"]}}, {"id": 2, "priority": 1, "action": {"type": "modifyHeaders", "requestHeaders": [{"header": "User-Agent", "operation": "set", "value": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"}]}, "condition": {"urlFilter": "*://*.claude.ai/*", "resourceTypes": ["xmlhttprequest", "main_frame", "sub_frame"]}}, {"id": 3, "priority": 1, "action": {"type": "modifyHeaders", "requestHeaders": [{"header": "User-Agent", "operation": "set", "value": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"}]}, "condition": {"urlFilter": "*://*.deepseek.com/*", "resourceTypes": ["xmlhttprequest", "main_frame", "sub_frame"]}}, {"id": 4, "priority": 1, "action": {"type": "modifyHeaders", "requestHeaders": [{"header": "User-Agent", "operation": "set", "value": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"}]}, "condition": {"urlFilter": "*://*.perplexity.ai/*", "resourceTypes": ["xmlhttprequest", "main_frame", "sub_frame"]}}, {"id": 5, "priority": 1, "action": {"type": "modifyHeaders", "requestHeaders": [{"header": "User-Agent", "operation": "set", "value": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"}]}, "condition": {"urlFilter": "*://copilot.microsoft.com/*", "resourceTypes": ["xmlhttprequest", "main_frame", "sub_frame"]}}]