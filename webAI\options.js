const WS_URL_KEY = 'webai_ws_url';
const DEFAULTS_KEY = 'webai_provider_defaults';

const wsStatus = document.getElementById('wsStatus');
const wsUrl = document.getElementById('wsUrl');
const saveBtn = document.getElementById('saveWs');
const refreshBtn = document.getElementById('refreshModels');
const modelsList = document.getElementById('models');
// API Keys UI
const keysStatus = document.getElementById('keysStatus');
const keysList = document.getElementById('keysList');
const createKeyBtn = document.getElementById('createKey');
const deleteSelectedBtn = document.getElementById('deleteSelected');

// Provider defaults UI elements
const pplxSearchFocus = document.getElementById('pplx-searchFocus');
const pplxSourcesGroup = document.getElementById('pplx-sources-group');
const pplxSourceWeb = document.getElementById('pplx-source-web');
const pplxSourceScholar = document.getElementById('pplx-source-scholar');
const pplxSourceSocial = document.getElementById('pplx-source-social');
const deepseekSearchEnabled = document.getElementById('deepseek-searchEnabled');
const claudeStyleKey = document.getElementById('claude-styleKey');

// Thread management UI elements
const refreshThreadsBtn = document.getElementById('refreshThreads');
const newThreadBtn = document.getElementById('newThread');
const threadsStatus = document.getElementById('threadsStatus');
const threadsList = document.getElementById('threadsList');

function fmtRel(ms) {
    const s = Math.max(0, Math.floor(ms / 1000));
    const h = Math.floor(s / 3600);
    const m = Math.floor((s % 3600) / 60);
    return `${h}h ${m}m`;
}

function renderKeys(state) {
    const { ok, data, error } = state || {};
    if (!ok) {
        keysStatus.textContent = error || 'Management channel not ready';
        keysList.innerHTML = '';
        createKeyBtn.disabled = true;
        deleteSelectedBtn.disabled = true;
        return;
    }
    const { keys = [], limit = 5 } = data || {};
    keysStatus.textContent = `Keys: ${keys.length}/${limit}`;
    keysList.innerHTML = '';
    const now = Date.now();
    for (const rec of keys) {
        const li = document.createElement('li');
        const left = document.createElement('div');
        left.style.display = 'flex'; left.style.alignItems = 'center'; left.style.gap = '8px';
        const cb = document.createElement('input');
        cb.type = 'checkbox'; cb.value = rec.key;
        const keySpan = document.createElement('span');
        keySpan.textContent = rec.key;
        const meta = document.createElement('div');
        meta.className = 'meta';
        const expMs = (rec.expiresAt || 0) - now;
        const abs = new Date(rec.expiresAt || 0).toLocaleString();
        meta.textContent = `expires in ${fmtRel(expMs)} • at ${abs}`;
        left.appendChild(cb);
        left.appendChild(keySpan);
        left.appendChild(meta);

        const right = document.createElement('div');
        const copyBtn = document.createElement('button');
        copyBtn.textContent = 'Copy';
        copyBtn.className = 'btn small';
        copyBtn.addEventListener('click', async () => {
            try { await navigator.clipboard.writeText(rec.key); copyBtn.textContent = 'Copied'; setTimeout(() => copyBtn.textContent = 'Copy', 1200); } catch { }
        });
        right.appendChild(copyBtn);

        li.appendChild(left);
        li.appendChild(right);
        keysList.appendChild(li);
    }
    createKeyBtn.disabled = keys.length >= (limit || 5);
    deleteSelectedBtn.disabled = keys.length === 0;
}

async function mgmt(op, extra = {}) {
    return new Promise((resolve) => {
        try {
            chrome.runtime.sendMessage({ type: 'MGMT_OP', op, extra }, (resp) => {
                if (chrome.runtime.lastError) {
                    console.warn('MGMT runtime error:', chrome.runtime.lastError.message);
                    resolve({ ok: false, error: 'Management channel not ready' });
                    return;
                }
                resolve(resp || { ok: false, error: 'No response' });
            });
        } catch (e) {
            console.warn('MGMT send error:', e);
            resolve({ ok: false, error: 'Send failed' });
        }
    });
}

async function refreshKeys() {
    const resp = await mgmt('keys.list');
    renderKeys(resp);
}

createKeyBtn.addEventListener('click', async () => {
    const resp = await mgmt('keys.create');
    renderKeys(resp);
    // Only refresh models if this was the first key created
    const state = await mgmt('keys.list');
    if (state?.ok && (state?.data?.keys || []).length === 1) {
        refreshModels();
    }
});

// Refresh models function
async function refreshModels() {
    modelsList.innerHTML = '<li>Loading models...</li>';
    const state = await mgmt('keys.list');
    if (!state?.ok || (state?.data?.keys || []).length === 0) {
        modelsList.innerHTML = '<li style="color: var(--muted);">No API key configured. Create an API key first.</li>';
        return;
    }

    // Get models from background
    try {
        chrome.runtime.sendMessage({ type: 'GET_MODELS' }, (resp) => {
            modelsList.innerHTML = '';
            if (chrome.runtime.lastError) {
                console.warn('Models runtime error:', chrome.runtime.lastError.message);
                modelsList.innerHTML = '<li style="color: var(--danger);">Error fetching models</li>';
                return;
            }
            const data = resp?.models || [];
            if (data.length === 0) {
                modelsList.innerHTML = '<li style="color: var(--muted);">No models available</li>';
                return;
            }
            for (const id of data) {
                const li = document.createElement('li');
                li.textContent = id;
                modelsList.appendChild(li);
            }
        });
    } catch (e) {
        console.warn('Models send error:', e);
        modelsList.innerHTML = '<li style="color: var(--danger);">Failed to request models</li>';
    }
}

refreshBtn.addEventListener('click', refreshModels);

deleteSelectedBtn.addEventListener('click', async () => {
    const selected = Array.from(keysList.querySelectorAll('input[type="checkbox"]:checked')).map(el => el.value);
    if (selected.length === 0) return;
    if (!confirm(`Delete ${selected.length} selected key(s)?`)) return;
    const resp = await mgmt('keys.delete', { keys: selected });
    renderKeys(resp);
});

// Initial try once the page loads (background will respond only after mgmt ready)
let hasInitialized = false;
setTimeout(() => {
    refreshKeys();
    if (!hasInitialized) {
        refreshModels();
        hasInitialized = true;
    }
    // Get initial WS status
    chrome.runtime.sendMessage({ type: 'GET_WS_STATUS' }, (resp) => {
        if (!chrome.runtime.lastError && resp) {
            setStatus(resp.connected);
        }
    });
}, 500);

chrome.runtime.onMessage.addListener((msg) => {
    if (msg && msg.type === 'MGMT_READY' && !hasInitialized) {
        refreshKeys();
        refreshModels();
        hasInitialized = true;
    }
    if (msg && msg.type === 'WS_STATUS') {
        setStatus(msg.connected);
    }
});


function setStatus(connected) {
    wsStatus.textContent = connected ? 'Connected' : 'Disconnected';
    wsStatus.classList.toggle('connected', connected);
}

// Load saved settings
chrome.storage.local.get([WS_URL_KEY, DEFAULTS_KEY], (res) => {
    wsUrl.value = res[WS_URL_KEY] || 'ws://localhost:11434/ws';

    // Load provider defaults
    const defaults = res[DEFAULTS_KEY] || {};
    const pplx = defaults['pplx-web'] || {};
    const deepseek = defaults['deepseek-web'] || {};
    const claude = defaults['claude-web'] || {};

    pplxSearchFocus.value = pplx.searchFocus || '';

    // Set search sources checkboxes
    const sources = Array.isArray(pplx.searchSources) ? pplx.searchSources : [];
    pplxSourceWeb.checked = sources.includes('web');
    pplxSourceScholar.checked = sources.includes('scholar');
    pplxSourceSocial.checked = sources.includes('social');

    // Show/hide sources based on focus
    updateSourcesVisibility();

    deepseekSearchEnabled.checked = !!deepseek.searchEnabled;
    claudeStyleKey.value = claude.style_key || '';
});
// Provider defaults immediate update functions
function updateProviderDefaults() {
    chrome.storage.local.get([DEFAULTS_KEY], (res) => {
        const defaults = res[DEFAULTS_KEY] || {};

        // Update Perplexity defaults
        const pplxSources = [];
        if (pplxSourceWeb.checked) pplxSources.push('web');
        if (pplxSourceScholar.checked) pplxSources.push('scholar');
        if (pplxSourceSocial.checked) pplxSources.push('social');

        defaults['pplx-web'] = {
            searchFocus: pplxSearchFocus.value.trim() || undefined,
            searchSources: pplxSources.length > 0 ? pplxSources : undefined
        };

        // Update Deepseek defaults
        defaults['deepseek-web'] = {
            searchEnabled: deepseekSearchEnabled.checked
        };

        // Update Claude defaults
        defaults['claude-web'] = {
            style_key: claudeStyleKey.value.trim() || undefined
        };

        // Save immediately
        chrome.storage.local.set({ [DEFAULTS_KEY]: defaults });
    });
}

// Function to show/hide search sources based on focus
function updateSourcesVisibility() {
    const focus = pplxSearchFocus.value;
    const showSources = focus === '' || focus === 'internet'; // Show for default (internet) and internet
    pplxSourcesGroup.style.display = showSources ? 'block' : 'none';
}

// Add event listeners for immediate updates
pplxSearchFocus.addEventListener('change', () => {
    updateSourcesVisibility();
    updateProviderDefaults();
});

// Thread management functions
async function refreshThreads() {
    threadsStatus.textContent = 'Loading threads...';
    threadsList.innerHTML = '';

    try {
        const response = await fetch(`${wsUrl.value.replace('ws://', 'http://').replace('/ws', '')}/v1/threads`, {
            headers: {
                'Authorization': `Bearer ${await getFirstApiKey()}`
            }
        });

        if (!response.ok) {
            throw new Error(`HTTP ${response.status}`);
        }

        const data = await response.json();
        const threads = data.threads || [];

        if (threads.length === 0) {
            threadsStatus.textContent = 'No conversations found';
            return;
        }

        threadsStatus.textContent = `${threads.length} conversation(s) found`;

        for (const thread of threads) {
            const li = document.createElement('li');
            li.className = 'thread-item';

            const info = document.createElement('div');
            info.className = 'thread-info';

            const title = document.createElement('div');
            title.className = 'thread-title';
            title.textContent = thread.title || 'Untitled Conversation';

            const meta = document.createElement('div');
            meta.className = 'thread-meta';
            const date = new Date(thread.updatedAt || thread.createdAt).toLocaleString();
            meta.textContent = `${thread.modelKey || 'Unknown'} • ${date} • ${thread.messages?.length || 0} messages`;

            info.appendChild(title);
            info.appendChild(meta);

            const actions = document.createElement('div');
            actions.className = 'thread-actions';

            const loadBtn = document.createElement('button');
            loadBtn.textContent = 'Load';
            loadBtn.className = 'btn small secondary';
            loadBtn.addEventListener('click', () => loadThread(thread.id, thread.modelKey));

            const deleteBtn = document.createElement('button');
            deleteBtn.textContent = 'Delete';
            deleteBtn.className = 'btn small danger';
            deleteBtn.addEventListener('click', () => deleteThread(thread.id, thread.modelKey));

            actions.appendChild(loadBtn);
            actions.appendChild(deleteBtn);

            li.appendChild(info);
            li.appendChild(actions);
            threadsList.appendChild(li);
        }
    } catch (error) {
        threadsStatus.textContent = `Error loading threads: ${error.message}`;
    }
}

async function getFirstApiKey() {
    const state = await mgmt('keys.list');
    if (state?.ok && state?.data?.keys?.length > 0) {
        return state.data.keys[0].key;
    }
    throw new Error('No API key available');
}

async function loadThread(threadId, modelKey) {
    try {
        const response = await fetch(`${wsUrl.value.replace('ws://', 'http://').replace('/ws', '')}/v1/threads/${threadId}/load`, {
            method: 'POST',
            headers: {
                'Authorization': `Bearer ${await getFirstApiKey()}`,
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ model: modelKey })
        });

        if (!response.ok) {
            throw new Error(`HTTP ${response.status}`);
        }

        alert(`Thread loaded successfully! You can now chat with this conversation using model: ${modelKey}`);
    } catch (error) {
        alert(`Error loading thread: ${error.message}`);
    }
}

async function deleteThread(threadId, modelKey) {
    if (!confirm('Are you sure you want to delete this conversation?')) return;

    try {
        const response = await fetch(`${wsUrl.value.replace('ws://', 'http://').replace('/ws', '')}/v1/threads/${threadId}`, {
            method: 'DELETE',
            headers: {
                'Authorization': `Bearer ${await getFirstApiKey()}`,
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ model: modelKey })
        });

        if (!response.ok) {
            throw new Error(`HTTP ${response.status}`);
        }

        alert('Thread deleted successfully!');
        refreshThreads(); // Refresh the list
    } catch (error) {
        alert(`Error deleting thread: ${error.message}`);
    }
}

async function createNewThread() {
    const model = prompt('Enter model name (e.g., gemini-web:2.0-flash, claude-web:sonnet):');
    if (!model) return;

    try {
        const response = await fetch(`${wsUrl.value.replace('ws://', 'http://').replace('/ws', '')}/v1/threads`, {
            method: 'POST',
            headers: {
                'Authorization': `Bearer ${await getFirstApiKey()}`,
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ model })
        });

        if (!response.ok) {
            throw new Error(`HTTP ${response.status}`);
        }

        alert('New thread created successfully!');
        refreshThreads(); // Refresh the list
    } catch (error) {
        alert(`Error creating thread: ${error.message}`);
    }
}

// Thread management event listeners
refreshThreadsBtn.addEventListener('click', refreshThreads);
newThreadBtn.addEventListener('click', createNewThread);

pplxSourceWeb.addEventListener('change', updateProviderDefaults);
pplxSourceScholar.addEventListener('change', updateProviderDefaults);
pplxSourceSocial.addEventListener('change', updateProviderDefaults);
deepseekSearchEnabled.addEventListener('change', updateProviderDefaults);
claudeStyleKey.addEventListener('change', updateProviderDefaults);

saveBtn.addEventListener('click', () => {
    const url = wsUrl.value.trim();
    if (!url) {
        alert('Please enter a WebSocket URL');
        return;
    }
    chrome.storage.local.set({ [WS_URL_KEY]: url }, () => {
        setStatus(false);
        // Show temporary feedback
        const originalText = saveBtn.textContent;
        saveBtn.textContent = 'Saved!';
        saveBtn.disabled = true;
        setTimeout(() => {
            saveBtn.textContent = originalText;
            saveBtn.disabled = false;
        }, 1500);
    });
});

// Old refresh button handler removed - now handled by refreshModels function above

