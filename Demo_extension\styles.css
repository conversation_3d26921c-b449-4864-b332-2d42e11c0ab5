body {
  width: 450px;
  padding: 15px;
  font-family: 'Segoe UI', Arial, sans-serif;
  color: #333;
  background-color: #f9f9f9;
}

#thread-title-container {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
}

#current-thread-title {
  flex-grow: 1;
  padding: 0.2% 0 1.5% 1%;
  font-size: large;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.icon-btn {
  background: none;
  border: none;
  cursor: pointer;
  font-size: 16px;
  padding: 4px 8px;
  margin: 0;
  width: auto;
}

.icon-btn:hover {
  background-color: #f0f0f0;
  border-radius: 4px;
}

.title-edit-container {
  display: flex;
  align-items: center;
  width: 100%;
  margin-bottom: 10px;
}

.title-edit-container input {
  flex-grow: 1;
  margin-right: 8px;
  margin-bottom: 0;
}

.title-edit-container button {
  width: auto;
  margin-bottom: 0;
  padding: 8px 12px;
}

select,
input,
textarea,
button {
  width: 100%;
  margin-bottom: 12px;
  padding: 10px;
  box-sizing: border-box;
  border: 1px solid #ddd;
  border-radius: 6px;
  font-size: 14px;
  transition: border-color 0.2s, box-shadow 0.2s;
}

select:focus,
input:focus,
textarea:focus {
  outline: none;
  border-color: #4a90e2;
  box-shadow: 0 0 0 2px rgba(74, 144, 226, 0.2);
}

#response {
  white-space: pre-wrap;
  border: 1px solid #ccc;
  padding: 10px;
  height: 250px;
  overflow-y: auto;
  margin-bottom: 10px;
  border-radius: 5px;
}

.loading {
  color: #888;
  font-style: italic;
}

.btn {
  background: #4a90e2;
  color: white;
  padding: 10px 16px;
  border-radius: 6px;
  margin: 4px;
  cursor: pointer;
  font-weight: 500;
  border: none;
  transition: background-color 0.2s, transform 0.1s;
}

.btn:hover {
  background: #3a7bc8;
}

.btn:active {
  transform: translateY(1px);
}

.conversation-item {
  padding: 8px;
  margin: 4px 0;
  border: 1px solid #e2e8f0;
  border-radius: 4px;
  cursor: pointer;
}

.conversation-item:hover {
  background: #f7fafc;
}

.hidden {
  display: none;
}

.button-row {
  display: flex;
  justify-content: space-between;
  margin-bottom: 10px;
}

.button-row button {
  flex: 1;
  margin: 0 5px;
}

.button-row button:first-child {
  margin-left: 0;
}

.button-row button:last-child {
  margin-right: 0;
}

.image-preview-container {
  position: relative;
  display: inline-block;
  margin-bottom: 10px;
}

#image-preview {
  display: flex;
  flex-direction: column;
  gap: 10px;
  margin: 10px 0 0 0;
}

.image-preview-item {
  position: relative;
  display: flex;
  align-items: center;
  background: #f8f9fa;
  border: 1px solid #e2e8f0;
  border-radius: 6px;
  padding: 8px 12px 8px 8px;
  min-height: 48px;
  margin-bottom: 0;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.03);
}

.preview-image {
  max-width: 40px;
  max-height: 40px;
  border-radius: 4px;
  margin-right: 12px;
}

.file-preview-icon {
  width: 40px;
  height: 40px;
  margin-right: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28px;
  background: #e6e6e6;
  border-radius: 4px;
}

.file-preview-name {
  font-size: 14px;
  color: #333;
  margin-right: 32px;
  word-break: break-all;
}

.remove-btn {
  position: absolute;
  top: 6px;
  right: 6px;
  background: #ff4d4d;
  color: white;
  border: none;
  border-radius: 50%;
  width: 22px;
  height: 22px;
  font-size: 14px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 2;
  transition: background 0.2s;
}

.remove-btn:hover {
  background: #d32f2f;
}

.message {
  padding: 8px 12px;
  margin: 8px 0;
  border-radius: 8px;
  max-width: 85%;
}

.user-message {
  background-color: #e2f5fe;
  align-self: flex-end;
  margin-left: auto;
}


#chat-container {
  display: flex;
  flex-direction: column;
}

.suggestions-container {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin-top: 8px;
  margin-bottom: 16px;
}

.suggestion-btn {
  background-color: #f0f0f0;
  border: 1px solid #ccc;
  border-radius: 16px;
  padding: 6px 12px;
  font-size: 12px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.suggestion-btn:hover {
  background-color: #e0e0e0;
}

/* Updated thread styles */
#conversations-list {
  margin-bottom: 15px;
  border: 1px solid #ddd;
  border-radius: 8px;
  overflow: hidden;
}

.threads-header {
  background-color: #4a90e2;
  color: white;
  padding: 10px 15px;
  font-weight: bold;
  border-bottom: 1px solid #ddd;
}

.threads-container {
  max-height: 300px;
  overflow-y: auto;
}

.thread-item {
  padding: 12px 15px;
  border-bottom: 1px solid #eee;
  cursor: pointer;
  transition: background-color 0.2s;
}

.thread-item:hover {
  background-color: #f5f8ff;
}

.thread-title {
  font-weight: bold;
  margin-bottom: 5px;
  color: #2d3748;
}

.thread-date {
  font-size: 12px;
  color: #718096;
}

.no-threads-msg {
  padding: 15px;
  color: #718096;
  font-style: italic;
  text-align: center;
}

/* Welcome message styling */
.welcome-message {
  text-align: center;
  color: #718096;
  font-size: 16px;
  margin: 30px auto 50px auto;
  padding: 20px;
  font-style: italic;
  max-width: 85%;
  position: relative;
  background-color: #f5f7fa;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.active-thread {
  background-color: #e6f7ff;
  border-left: 4px solid #4a90e2;
}

.active-thread .thread-title {
  color: #4a90e2;
}

/* Make sure this doesn't conflict with the hover state */
.active-thread:hover {
  background-color: #d6f0ff;
}

.mode-toggle-container {
  margin: 10px 0;
  display: flex;
  justify-content: flex-end;
}

.mode-toggle {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
}

#bing-mode-select {
  padding: 4px 8px;
  border-radius: 4px;
  border: 1px solid #ccc;
  background-color: white;
}

.mode-info {
  cursor: help;
  color: #3498db;
  font-weight: bold;
}

.hidden {
  display: none !important;
}

.error-message {
  background-color: #ffebee;
  border-left: 4px solid #f44336;
  color: #d32f2f;
  padding: 10px 15px;
  margin: 10px 0;
  border-radius: 4px;
  font-size: 14px;
  line-height: 1.5;
  white-space: pre-wrap;
  word-break: break-word;
}

/* Claude Styles Container */
#claude-styles-container {
  margin-bottom: 15px;
  padding: 12px;
  background-color: #f8f5ff;
  border-radius: 8px;
  border: 1px solid #e6d8ff;
}

#claude-style-select {
  width: 100%;
  padding: 10px;
  border-radius: 6px;
  border: 1px solid #ddd;
  background-color: white;
}

.style-item {
  display: flex;
  align-items: center;
  padding: 8px 12px;
  margin: 8px 0;
  border-radius: 8px;
  background-color: #f8f9fa;
  border: 1px solid #e2e8f0;
}

.style-icon {
  margin-right: 10px;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  background-color: #e2e8f0;
  color: #4a5568;
}

.style-info {
  flex: 1;
}

.style-name {
  font-weight: bold;
  margin-bottom: 4px;
}

.style-description {
  font-size: 12px;
  color: #718096;
}

/* Add these styles for the share button and modal */
#share-conversation-btn {
  margin-left: 5px;
}

.modal-container {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.modal-content {
  background-color: white;
  padding: 20px;
  border-radius: 8px;
  max-width: 90%;
  width: 400px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.modal-header h3 {
  margin: 0;
}

.modal-body p {
  margin-top: 0;
}

/* Add these styles for the delete button */
#delete-conversation-btn {
  margin-left: 5px;
  color: #d32f2f;
}

#delete-conversation-btn:hover {
  background-color: #ffebee;
}

/* Perplexity Options Styling */
#perplexity-options-container {
  margin-bottom: 15px;
  padding: 15px;
  background-color: #f0f7ff;
  border-radius: 8px;
  border: 1px solid #d0e3ff;
}

.option-group {
  margin-bottom: 16px;
}

.option-group:last-child {
  margin-bottom: 0;
}

#perplexity-options-container label {
  display: block;
  margin-bottom: 8px;
  font-weight: 500;
  color: #2c5282;
}

#perplexity-sources-container {
  margin-top: 12px;
}

#perplexity-sources-checkboxes {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
  margin-top: 10px;
}

.sources-wrapper {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
  width: 100%;
}

/* Source Button Styling */
.toggle-switch-label {
  display: inline-block;
  padding: 8px 12px;
  background-color: white;
  border-radius: 20px;
  border: 1px solid #e2e8f0;
  cursor: pointer;
  transition: all 0.2s ease;
  margin: 4px;
  font-size: 14px;
  font-weight: 500;
  color: #4a5568;
  text-align: center;
}

.toggle-switch-label:hover {
  background-color: #edf2f7;
  border-color: #cbd5e0;
}

.toggle-switch-label input[type="checkbox"] {
  position: absolute;
  opacity: 0;
  cursor: pointer;
  height: 0;
  width: 0;
}

.toggle-switch-label.active {
  background-color: #e6f7ff;
  border-color: #4a90e2;
  color: #4a90e2;
}

#perplexity-model-select {
  background-color: white;
}


.deepseek-toggle-row {
  display: flex;
  gap: 16px;
  justify-content: space-between;
}

.deepseek-toggle-row .mode-toggle-container {
  flex: 1;
}

.deepseek-toggle-row .mode-toggle-container label {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: flex-start;
}

.deepseek-toggle-row .mode-toggle-container:last-child label {
  justify-content: flex-end;
}

/* Removed styles for the old global #reasoning-container */
/* --- Per-Message Reasoning Styles --- */
.message-reasoning-container {
  background-color: #303338;
  /* Dark background like reference */
  color: #b9bbbe;
  /* Greyish text */
  border-radius: 6px;
  margin-bottom: 6px;
  /* Space between reasoning and main message */
  font-size: 0.85em;
  border: 1px solid #40444b;
  /* Subtle border */
  overflow: hidden;
  /* Keep content contained */
  /* margin-top: 20px; */
  white-space: normal;
}

.message-reasoning-header {
  display: flex;
  align-items: center;
  padding: 5px 10px;
  cursor: pointer;
  background-color: #36393f;
  /* Slightly lighter header */
  border-bottom: 1px solid #40444b;
  /* Separator line */
  transition: background-color 0.2s;
}

.message-reasoning-header:hover {
  background-color: #40444b;
}

.message-reasoning-header .toggle-icon {
  margin-left: auto;
  /* Pushes icon to the right */
  font-size: 1.0em;
  /* Adjusted size */
  transition: transform 0.2s ease-in-out;
  display: inline-block;
  /* Ensure transform works */
}

.message-reasoning-container.collapsed .toggle-icon {
  transform: rotate(-180deg);
}

.message-reasoning-header .thought-icon {
  margin-right: 6px;
  font-size: 0.9em;
  /* Adjust icon size if needed */
}

.message-reasoning-content {
  padding: 8px 12px;
  white-space: pre-wrap;
  /* Preserve whitespace and line breaks */
  word-break: break-word;
  max-height: 150px;
  /* Limit initial height */
  overflow-y: auto;
  /* Add scroll if needed */
  transition: max-height 0.3s ease-out, padding 0.3s ease-out, opacity 0.3s ease-out;
  border-top: none;
  /* Avoid double border */
  opacity: 1;
}

.message-reasoning-container.collapsed .message-reasoning-content {
  max-height: 0;
  padding-top: 0;
  padding-bottom: 0;
  opacity: 0;
  overflow: hidden;
}

.assistant-message>.message-text-content {
  background-color: #f0f0f0;
  border-radius: 6px;
  padding: 8px 8px 10px 8px;
}


.assistant-message {
  background-color: transparent;
  align-self: flex-start;
  padding: 8px 8px;
  width: 80%;
}

/* --- Per-Message Reasoning Styles --- */
.message-reasoning-container {
  background-color: #303338;
  /* Dark background like reference */
  color: #b9bbbe;
  /* Greyish text */
  border-radius: 6px;
  margin-bottom: 6px;
  /* Space between reasoning and main message */
  font-size: 90%;
  border: 1px solid #40444b;
  /* Subtle border */
  overflow: hidden;
  /* Keep content contained */
}

.message-reasoning-header {
  display: flex;
  align-items: center;
  padding: 5px 10px;
  cursor: pointer;
  background-color: #36393f;
  /* Slightly lighter header */
  border-bottom: 1px solid #40444b;
  /* Separator line */
  transition: background-color 0.2s;
}

.message-reasoning-header:hover {
  background-color: #40444b;
}


.message-reasoning-content {
  padding: 8px 12px;
  white-space: pre-wrap;
  /* Preserve whitespace and line breaks */
  word-break: break-word;
  max-height: 150px;
  /* Limit initial height */
  overflow-y: auto;
  /* Add scroll if needed */
  transition: max-height 0.3s ease-out, padding 0.3s ease-out, opacity 0.3s ease-out;
  border-top: none;
  /* Avoid double border */
  opacity: 1;
}

.message-reasoning-container.collapsed .message-reasoning-content {
  max-height: 0;
  padding-top: 0;
  padding-bottom: 0;
  opacity: 0;
  overflow: hidden;
}




#file-upload-btn::-webkit-file-upload-button {
  visibility: visible;
}

#file-upload-btn {
  color: transparent;
  width: auto;
  min-width: 110px;
  border: 0;
  border-radius: 0.5px;
  padding: 0;
  margin: 0;
}

/* File Upload Row Styles */
.file-upload-row {
  display: flex;
  align-items: center;
  justify-content: space-between;
  border: 1px solid #ddd;
  border-radius: 6px;
  padding: 10px;
  background: #fff;
  width: 100%;
  box-sizing: border-box;
  margin-bottom: 10px;
}

.file-upload-row input[type="file"] {
  width: auto !important;
  min-width: 0 !important;
  flex-shrink: 0;
  text-indent: 62%;
}

.attachments-label {
  font-size: 15px;
  color: #444;
}

#file-upload-btn {
  margin-left: auto;
}

.loading-spinner {
  border: 3px solid #f3f3f3;
  border-top: 3px solid #3498db;
  border-radius: 50%;
  width: 16px;
  height: 16px;
  animation: spin 1s linear infinite;
  display: inline-block;
  vertical-align: middle;
  margin-right: 8px;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}

#prompt {
  resize: vertical;
  min-height: 90px;
}