# WebAI Server

Local server that exposes OpenAI-compatible endpoints and bridges to the WebAI browser extension via WebSocket.

## Endpoints

- POST /keys/new -> { api_key, expires_at }
- GET /v1/models (alias /models)
- POST /v1/chat/completions (alias /chat/completions)

All endpoints except / and /keys/new require Authorization: Bearer <api_key>.

## WebSocket Bridge

- Path: ws://localhost:11434/ws
- The browser extension connects to this WS and handles model execution.

## Development

1. npm install
2. npm run start
3. Generate an API key: curl -X POST http://localhost:11434/keys/new
4. Use the key with your client, e.g.

```
curl http://localhost:11434/v1/models -H "Authorization: Bearer <KEY>"
```

For chat streaming, send POST to /v1/chat/completions with `stream:true` in the JSON body.

