:root {
    --bg: #0b0f17;
    --panel: #121826;
    --muted: #9aa5b1;
    --text: #dbe2ea;
    --accent: #4f46e5;
    --accent-2: #22c55e;
    --danger: #ef4444;
    --border: #1f2937;
}

body {
    font-family: system-ui, -apple-system, Segoe UI, Roboto, Arial, sans-serif;
    margin: 0;
    background: var(--bg);
    color: var(--text);
}

.container {
    padding: 16px;
    max-width: 900px;
    margin: 0 auto;
}

header {
    display: flex;
    align-items: center;
    justify-content: space-between;
}

h1 {
    font-size: 20px;
    margin: 0;
}

.badge {
    padding: 2px 8px;
    border-radius: 10px;
    background: #374151;
    color: var(--muted);
}

.badge.connected {
    background: rgba(34, 197, 94, .15);
    color: var(--accent-2);
}

section {
    margin-top: 20px;
}

.row {
    display: flex;
    gap: 8px;
    align-items: center;
    margin-bottom: 8px;
}

h2 {
    font-size: 16px;
    margin: 0 0 12px 0;
    color: var(--text);
}

input[type="text"] {
    background: var(--panel);
    border: 1px solid var(--border);
    border-radius: 8px;
    padding: 8px 12px;
    color: var(--text);
    flex: 1;
}

input[type="text"]:focus {
    outline: none;
    border-color: var(--accent);
    box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
}

input[type="checkbox"] {
    accent-color: var(--accent);
    transform: scale(1.1);
}

label {
    color: var(--muted);
    font-size: 14px;
    font-weight: 500;
    margin-bottom: 6px;
    display: block;
}

.server-config {
    background: var(--panel);
    border: 1px solid var(--border);
    border-radius: 12px;
    padding: 16px;
}

.input-group {
    margin-bottom: 16px;
}

.input-row {
    display: flex;
    gap: 12px;
    align-items: center;
}

.input-row input {
    flex: 1;
}

.actions-row {
    display: flex;
    gap: 12px;
    align-items: center;
    padding-top: 12px;
    border-top: 1px solid var(--border);
}

.card {
    background: var(--panel);
    border: 1px solid var(--border);
    border-radius: 12px;
    padding: 12px;
}

.list {
    list-style: none;
    padding: 0;
    margin: 0;
}

.list li {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 8px;
    padding: 12px 16px;
    border: 1px solid var(--border);
    border-radius: 10px;
    margin-bottom: 10px;
    background: #0f1626;
    transition: all 0.2s ease;
}

.list li:hover {
    border-color: #374151;
    background: #1a202c;
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.list .meta {
    color: var(--muted);
    font-size: 12px;
}

.btn {
    background: #1f2937;
    color: var(--text);
    border: 1px solid var(--border);
    padding: 8px 16px;
    border-radius: 8px;
    cursor: pointer;
    font-size: 14px;
    font-weight: 500;
    transition: all 0.2s ease;
    position: relative;
    overflow: hidden;
}

.btn:hover {
    border-color: #475569;
    background: #374151;
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.btn:active {
    transform: translateY(0);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.btn.primary {
    background: var(--accent);
    border-color: var(--accent);
    color: white;
}

.btn.primary:hover {
    background: #6366f1;
    border-color: #6366f1;
    box-shadow: 0 4px 12px rgba(79, 70, 229, 0.3);
}

.btn.primary:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
}

.btn.primary:disabled:hover {
    background: var(--accent);
    border-color: var(--accent);
    transform: none;
    box-shadow: none;
}

.btn.danger {
    background: transparent;
    border-color: var(--danger);
    color: #fecaca;
}

.btn.danger:hover {
    background: rgba(239, 68, 68, 0.1);
    border-color: #f87171;
    color: #fca5a5;
    box-shadow: 0 4px 12px rgba(239, 68, 68, 0.2);
}

.btn.small {
    padding: 6px 12px;
    font-size: 12px;
    border-radius: 6px;
}

.btn.secondary {
    background: var(--panel);
    border-color: var(--border);
    color: var(--text);
}

.btn.secondary:hover {
    background: #1e293b;
    border-color: #475569;
}

.note {
    color: var(--muted);
    font-size: 12px;
    margin: 6px 0;
}

#models {
    list-style: none;
    padding: 0;
    margin: 0;
}

#models li {
    padding: 8px 12px;
    border: 1px solid var(--border);
    border-radius: 8px;
    margin-bottom: 8px;
    background: var(--panel);
    font-family: 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace;
    font-size: 13px;
}

footer {
    margin-top: 40px;
    padding-top: 20px;
    border-top: 1px solid var(--border);
    text-align: center;
}

footer small {
    color: var(--muted);
    font-size: 12px;
}

.provider-defaults {
    background: var(--panel);
    border: 1px solid var(--border);
    border-radius: 12px;
    padding: 16px;
}

.provider-group {
    margin-bottom: 24px;
    padding-bottom: 20px;
    border-bottom: 1px solid var(--border);
}

.provider-group:last-child {
    margin-bottom: 0;
    padding-bottom: 0;
    border-bottom: none;
}

.provider-group h3 {
    font-size: 14px;
    font-weight: 600;
    color: var(--text);
    margin: 0 0 12px 0;
}

.provider-group .input-group {
    margin-bottom: 12px;
}

.provider-group .input-group:last-child {
    margin-bottom: 0;
}

label input[type="checkbox"] {
    margin-right: 8px;
}

label {
    display: flex;
    align-items: center;
    cursor: pointer;
}

label:has(input[type="text"]),
label:has(select) {
    display: block;
    cursor: default;
}

select {
    background: var(--panel);
    border: 1px solid var(--border);
    border-radius: 8px;
    padding: 8px 12px;
    color: var(--text);
    width: 100%;
    cursor: pointer;
}

select:focus {
    outline: none;
    border-color: var(--accent);
    box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
}

.checkbox-group {
    display: flex;
    flex-direction: column;
    gap: 8px;
    margin-top: 6px;
}

.checkbox-group label {
    display: flex;
    align-items: center;
    cursor: pointer;
    font-size: 14px;
}

.checkbox-group input[type="checkbox"] {
    margin-right: 8px;
}

.thread-management {
    background: var(--panel);
    border: 1px solid var(--border);
    border-radius: 12px;
    padding: 16px;
}

.thread-list {
    margin-top: 16px;
}

.thread-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 12px 16px;
    border: 1px solid var(--border);
    border-radius: 8px;
    margin-bottom: 8px;
    background: #0f1626;
    transition: all 0.2s ease;
}

.thread-item:hover {
    border-color: #374151;
    background: #1a202c;
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.thread-info {
    flex: 1;
}

.thread-title {
    font-weight: 500;
    color: var(--text);
    margin-bottom: 4px;
}

.thread-meta {
    font-size: 12px;
    color: var(--muted);
}

.thread-actions {
    display: flex;
    gap: 8px;
}