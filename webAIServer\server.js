// WebAI Server - OpenAI-compatible API with WebSocket bridge to the WebAI extension
// Minimal scaffolding with HTTP endpoints and WS skeleton.

import express from 'express';
import fs from 'fs';
import path from 'path';
import crypto from 'crypto';
import { fileURLToPath } from 'url';

// Encrypted API key store (persisted across restarts)
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);
const DATA_DIR = path.join(__dirname, 'data');
const SECRET_PATH = path.join(DATA_DIR, 'keys.secret');
const STORE_PATH = path.join(DATA_DIR, 'keys.enc');
const MAX_KEYS = 5;

function ensureDataDir() {
    try { fs.mkdirSync(DATA_DIR, { recursive: true }); } catch { }
}

function loadSecret() {
    ensureDataDir();
    if (fs.existsSync(SECRET_PATH)) {
        return fs.readFileSync(SECRET_PATH);
    }
    const key = crypto.randomBytes(32);
    fs.writeFileSync(SECRET_PATH, key);
    return key;
}

const SECRET = loadSecret();

// Mgmt channel helpers: derive session key and AES-GCM enc/dec compatible with WebCrypto
function hkdfLike(shared, serverNonce, clientNonce) {
    return crypto.createHash('sha256').update(Buffer.concat([shared, serverNonce, clientNonce])).digest();
}
function encMgmt(obj, keyBuf) {
    const iv = crypto.randomBytes(12);
    const cipher = crypto.createCipheriv('aes-256-gcm', keyBuf, iv);
    const pt = Buffer.from(JSON.stringify(obj), 'utf8');
    const enc = Buffer.concat([cipher.update(pt), cipher.final()]);
    const tag = cipher.getAuthTag();
    const data = Buffer.concat([enc, tag]);
    return { iv: iv.toString('base64'), data: data.toString('base64') };
}
function decMgmt(payload, keyBuf) {
    const iv = Buffer.from(payload.iv, 'base64');
    const data = Buffer.from(payload.data, 'base64');
    const tag = data.subarray(data.length - 16);
    const ciphertext = data.subarray(0, data.length - 16);
    const decipher = crypto.createDecipheriv('aes-256-gcm', keyBuf, iv);
    decipher.setAuthTag(tag);
    const dec = Buffer.concat([decipher.update(ciphertext), decipher.final()]);
    return JSON.parse(dec.toString('utf8'));
}

function encryptJSON(obj) {
    const iv = crypto.randomBytes(12);
    const cipher = crypto.createCipheriv('aes-256-gcm', SECRET, iv);
    const plaintext = Buffer.from(JSON.stringify(obj), 'utf8');
    const enc = Buffer.concat([cipher.update(plaintext), cipher.final()]);
    const tag = cipher.getAuthTag();
    const payload = { iv: iv.toString('base64'), tag: tag.toString('base64'), data: enc.toString('base64') };
    return JSON.stringify(payload);
}

function decryptJSON(text) {
    const payload = JSON.parse(text);
    const iv = Buffer.from(payload.iv, 'base64');
    const tag = Buffer.from(payload.tag, 'base64');
    const data = Buffer.from(payload.data, 'base64');
    const decipher = crypto.createDecipheriv('aes-256-gcm', SECRET, iv);
    decipher.setAuthTag(tag);
    const dec = Buffer.concat([decipher.update(data), decipher.final()]);
    return JSON.parse(dec.toString('utf8'));
}

function loadKeysFromStore() {
    try {
        if (!fs.existsSync(STORE_PATH)) return [];
        const enc = fs.readFileSync(STORE_PATH, 'utf8');
        const arr = decryptJSON(enc);
        return Array.isArray(arr) ? arr : [];
    } catch (e) {
        console.error('Failed to load key store:', e);
        return [];
    }
}

function saveKeysToStore(keysArr) {
    try {
        ensureDataDir();
        const enc = encryptJSON(keysArr);
        fs.writeFileSync(STORE_PATH, enc, 'utf8');
    } catch (e) {
        console.error('Failed to save key store:', e);
    }
}

function getKeysArrayFromMap(map) {
    return Array.from(map.entries()).map(([key, meta]) => ({ key, ...meta }));
}

function hydrateMapFromArray(arr, map) {
    map.clear();
    for (const rec of arr) {
        if (rec && rec.key) {
            const { key, createdAt, ttlMs, type } = rec;
            map.set(key, { createdAt, ttlMs, type: type || 'general' });
        }
    }
}

// In-memory API key store (temporary keys)
const API_KEYS = new Map(); // key -> { createdAt:number, ttlMs:number }

// Initialize API_KEYS from store on boot
ensureDataDir();
hydrateMapFromArray(loadKeysFromStore(), API_KEYS);

import cors from 'cors';
import { WebSocketServer } from 'ws';
import { v4 as uuidv4 } from 'uuid';

const PORT = process.env.PORT ? Number(process.env.PORT) : 11434; // default localhost port
const WS_PATH = '/ws';
const DEFAULT_TTL_MS = 8 * 60 * 60 * 1000; // 8 hours

// In-memory extension connections
let extensionClient = null; // single extension expected; can expand to multiple if needed

// Lightweight models cache
let MODELS_CACHE = { data: [], ts: 0 };
const MODELS_CACHE_TTL_MS = 5000;

// Express setup
const app = express();
app.use(cors({
    origin: [
        /^http:\/\/localhost(:\d+)?$/,
        'chrome-extension://ncbgfpogefbcpllkfcjgmgiehljfgicp'
    ],
    credentials: false
}));
app.use(express.json({ limit: '20mb' }));

// Helper: create API key (used only by secure WS mgmt messages)
function createApiKey(ttlMs = DEFAULT_TTL_MS) {
    const key = uuidv4();
    API_KEYS.set(key, { createdAt: Date.now(), ttlMs, type: 'general' });
    if (API_KEYS.size > MAX_KEYS) {
        const oldest = [...API_KEYS.entries()].sort((a, b) => a[1].createdAt - b[1].createdAt)[0];
        if (oldest) API_KEYS.delete(oldest[0]);
    }
    saveKeysToStore(getKeysArrayFromMap(API_KEYS));
    return { api_key: key, expires_at: new Date(Date.now() + ttlMs).toISOString() };
}

function validateApiKey(req, res, next) {
    const auth = req.headers['authorization'] || '';
    const token = auth.startsWith('Bearer ') ? auth.slice(7) : null;
    if (!token || !API_KEYS.has(token)) {
        return res.status(401).json({ error: { type: 'authentication_error', message: 'Invalid or missing API key' } });
    }
    const meta = API_KEYS.get(token);
    if (Date.now() > meta.createdAt + meta.ttlMs) {
        API_KEYS.delete(token);
        saveKeysToStore(getKeysArrayFromMap(API_KEYS));
        return res.status(401).json({ error: { type: 'authentication_error', message: 'API key expired' } });
    }
    next();
}

// Health
app.get('/', (_req, res) => res.json({ ok: true, message: 'WebAI Server running', ws: `ws://localhost:${PORT}${WS_PATH}` }));

// Image proxy for restricted origins
app.get('/proxy-image', async (req, res) => {
    const { url } = req.query;
    if (!url) return res.status(400).json({ error: 'Missing url parameter' });

    try {
        const parsedUrl = new URL(url);
        if (!['http:', 'https:'].includes(parsedUrl.protocol)) {
            return res.status(400).json({ error: 'Only http/https URLs allowed' });
        }

        const hostname = parsedUrl.hostname.toLowerCase();
        if (hostname === 'localhost' || hostname === '127.0.0.1' || hostname === '::1' ||
            hostname.startsWith('192.168.') || hostname.startsWith('10.') ||
            hostname.startsWith('172.')) {
            return res.status(400).json({ error: 'Localhost/private IPs not allowed' });
        }

        const response = await fetch(url, {
            headers: { 'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36' },
            timeout: 10000
        });

        if (!response.ok) {
            return res.status(response.status).json({ error: `Failed to fetch image: ${response.statusText}` });
        }

        const contentType = response.headers.get('content-type') || '';
        if (!contentType.startsWith('image/')) {
            return res.status(400).json({ error: 'URL does not point to an image' });
        }

        res.setHeader('Content-Type', contentType);
        res.setHeader('Cache-Control', 'public, max-age=3600');
        response.body.pipe(res);

    } catch (error) {
        console.error('Image proxy error:', error);
        res.status(500).json({ error: 'Failed to proxy image' });
    }
});

// No public HTTP endpoints for key creation/deletion

// No public HTTP endpoints for key retrieval

// Models endpoints (OpenAI-compatible)
app.get(['/v1/models', '/models'], validateApiKey, async (_req, res) => {
    if (!extensionClient || extensionClient.readyState !== 1) {
        // Serve cache if present and fresh
        if (Date.now() - MODELS_CACHE.ts < MODELS_CACHE_TTL_MS && MODELS_CACHE.data.length) {
            return res.json({ object: 'list', data: MODELS_CACHE.data });
        }
        return res.status(503).json({ error: { type: 'service_unavailable', message: 'Extension not connected' } });
    }

    // Serve cache if fresh enough
    if (Date.now() - MODELS_CACHE.ts < MODELS_CACHE_TTL_MS && MODELS_CACHE.data.length) {
        return res.json({ object: 'list', data: MODELS_CACHE.data });
    }

    const requestId = uuidv4();
    const msg = { type: 'request.models', requestId };

    try {
        const result = await sendAndWait(extensionClient, requestId, msg, 5000);
        const data = (result.models || []).map(id => ({ id, object: 'model', created: Math.floor(Date.now() / 1000), owned_by: 'webai' }));
        MODELS_CACHE = { data, ts: Date.now() };
        res.json({ object: 'list', data });
    } catch (err) {
        res.status(502).json({ error: { type: 'bad_gateway', message: String(err?.message || err) } });
    }
});

// Thread Management endpoints
app.get('/v1/threads', validateApiKey, async (_req, res) => {
    if (!extensionClient || extensionClient.readyState !== 1) {
        return res.status(503).json({ error: { type: 'service_unavailable', message: 'Extension not connected' } });
    }

    const requestId = uuidv4();
    const msg = { type: 'request.threads.list', requestId };

    try {
        const result = await sendAndWait(extensionClient, requestId, msg, 10000);
        res.json({ threads: result.threads || [] });
    } catch (err) {
        res.status(502).json({ error: { type: 'bad_gateway', message: String(err?.message || err) } });
    }
});

app.post('/v1/threads', validateApiKey, async (req, res) => {
    if (!extensionClient || extensionClient.readyState !== 1) {
        return res.status(503).json({ error: { type: 'service_unavailable', message: 'Extension not connected' } });
    }

    const { model } = req.body || {};
    if (!model) {
        return res.status(400).json({ error: { type: 'invalid_request_error', message: 'model is required' } });
    }

    const requestId = uuidv4();
    const msg = { type: 'request.threads.new', requestId, model };

    try {
        const result = await sendAndWait(extensionClient, requestId, msg, 10000);
        res.json({ thread: result.thread || {} });
    } catch (err) {
        res.status(502).json({ error: { type: 'bad_gateway', message: String(err?.message || err) } });
    }
});

app.post('/v1/threads/:threadId/load', validateApiKey, async (req, res) => {
    if (!extensionClient || extensionClient.readyState !== 1) {
        return res.status(503).json({ error: { type: 'service_unavailable', message: 'Extension not connected' } });
    }

    const { threadId } = req.params;
    const { model } = req.body || {};
    if (!threadId || !model) {
        return res.status(400).json({ error: { type: 'invalid_request_error', message: 'threadId and model are required' } });
    }

    const requestId = uuidv4();
    const msg = { type: 'request.threads.load', requestId, threadId, model };

    try {
        const result = await sendAndWait(extensionClient, requestId, msg, 10000);
        res.json({ thread: result.thread || {} });
    } catch (err) {
        res.status(502).json({ error: { type: 'bad_gateway', message: String(err?.message || err) } });
    }
});

app.delete('/v1/threads/:threadId', validateApiKey, async (req, res) => {
    if (!extensionClient || extensionClient.readyState !== 1) {
        return res.status(503).json({ error: { type: 'service_unavailable', message: 'Extension not connected' } });
    }

    const { threadId } = req.params;
    const { model } = req.body || {};
    if (!threadId || !model) {
        return res.status(400).json({ error: { type: 'invalid_request_error', message: 'threadId and model are required' } });
    }

    const requestId = uuidv4();
    const msg = { type: 'request.threads.delete', requestId, threadId, model };

    try {
        await sendAndWait(extensionClient, requestId, msg, 10000);
        res.json({ deleted: true });
    } catch (err) {
        res.status(502).json({ error: { type: 'bad_gateway', message: String(err?.message || err) } });
    }
});

// Chat Completions (OpenAI-compatible)
app.post(['/v1/chat/completions', '/chat/completions'], validateApiKey, async (req, res) => {
    /////////////// Log the full request body
    try {
        console.log('--- Incoming /chat/completions request body ---');
        console.log(JSON.stringify(req.body, null, 2));
        console.log('--- End of request body ---');
    } catch (e) {
        console.error('Error logging request body:', e);
    }
    if (!extensionClient || extensionClient.readyState !== 1) {
        return res.status(503).json({ error: { type: 'service_unavailable', message: 'Extension not connected' } });
    }

    const { model, messages, stream } = req.body || {};
    if (!model || !Array.isArray(messages)) {
        return res.status(400).json({ error: { type: 'invalid_request_error', message: 'model and messages are required' } });
    }

    const requestId = uuidv4();
    const msg = { type: 'request.start', requestId, endpoint: 'chat.completions', payload: { model, messages, stream: !!stream } };

    if (stream) {
        // SSE setup
        res.status(200);
        res.setHeader('Content-Type', 'text/event-stream');
        res.setHeader('Cache-Control', 'no-cache, no-transform');
        res.setHeader('Connection', 'keep-alive');
        res.flushHeaders?.();

        let closed = false;
        const abortIfOpen = () => {
            if (!closed && extensionClient && extensionClient.readyState === 1) {
                try { extensionClient.send(JSON.stringify({ type: 'request.abort', requestId })); } catch { }
            }
            closed = true;
        };
        req.on('aborted', abortIfOpen);
        res.on('close', abortIfOpen);

        // Maintain previous cumulative text to compute deltas
        let previous = '';
        const cleanup = registerResponseHandlers(requestId, {
            onChunk: (data) => {
                if (closed) return;
                const full = data?.text ?? '';
                // Improved delta: longest common prefix
                let lcp = 0;
                const minLen = Math.min(previous.length, full.length);
                while (lcp < minLen && previous.charCodeAt(lcp) === full.charCodeAt(lcp)) lcp++;
                const delta = full.slice(lcp);
                previous = full;
                const chunk = {
                    id: `chatcmpl-${requestId}`,
                    object: 'chat.completion.chunk',
                    created: Math.floor(Date.now() / 1000),
                    model,
                    choices: [
                        { index: 0, delta: (delta ? { content: delta } : {}), finish_reason: null }
                    ]
                };
                res.write(`data: ${JSON.stringify(chunk)}\n\n`);
            },
            onEnd: () => {
                if (closed) return;
                const doneChunk = {
                    id: `chatcmpl-${requestId}`,
                    object: 'chat.completion.chunk',
                    created: Math.floor(Date.now() / 1000),
                    model,
                    choices: [{ index: 0, delta: {}, finish_reason: 'stop' }]
                };
                res.write(`data: ${JSON.stringify(doneChunk)}\n\n`);
                res.write('data: [DONE]\n\n');
                res.end();
                closed = true;
            },
            onError: (error) => {
                if (closed) return;
                const errType = mapErrorCodeToOpenAIType(error?.code);
                const errObj = { error: { type: errType, message: String(error?.message || error) } };
                res.write(`data: ${JSON.stringify(errObj)}\n\n`);
                res.write('data: [DONE]\n\n');
                res.end();
                closed = true;
            }
        });

        try {
            extensionClient.send(JSON.stringify(msg));
        } catch (e) {
            cleanup();
            return res.end();
        }
        // connection kept open; cleanup when closed by onEnd/onError
        return;
    }

    try {
        let closed = false;
        const abortIfOpen = () => {
            if (!closed && extensionClient && extensionClient.readyState === 1) {
                try { extensionClient.send(JSON.stringify({ type: 'request.abort', requestId })); } catch { }
            }
            closed = true;
        };
        req.on('aborted', abortIfOpen);
        res.on('close', abortIfOpen);

        const result = await sendAndWait(extensionClient, requestId, msg, 120000);
        if (closed) return; // client already gone
        const text = result?.text ?? '';
        const out = {
            id: `chatcmpl-${requestId}`,
            object: 'chat.completion',
            created: Math.floor(Date.now() / 1000),
            model,
            choices: [{ index: 0, message: { role: 'assistant', content: text }, finish_reason: 'stop' }],
        };
        res.json(out);
    } catch (err) {
        const type = mapErrorCodeToOpenAIType(err?.code);
        const status = mapErrorCodeToStatus(err?.code);
        res.status(status).json({ error: { type, message: String(err?.message || err) } });
    }
});

// --- WS server ---
const server = app.listen(PORT, () => {
    console.log(`WebAI Server listening on http://localhost:${PORT}`);
});

const wss = new WebSocketServer({ server, path: WS_PATH });

// Pending request promises
const WAITERS = new Map(); // requestId -> { resolve, reject, timeout }
// Streaming handlers
const STREAM_HANDLERS = new Map(); // requestId -> { onChunk, onEnd, onError }

wss.on('connection', (ws) => {
    extensionClient = ws; // single client for now
    console.log('Extension connected via WS');

    // --- Management channel handshake ---
    const ecdh = crypto.createECDH('prime256v1');
    ecdh.generateKeys();
    const serverNonce = crypto.randomBytes(16);
    ws.mgmt = { sessionKey: null, serverNonce };
    ws.send(JSON.stringify({ type: 'mgmt.hello', pub: ecdh.getPublicKey().toString('base64'), nonce: serverNonce.toString('base64') }));

    ws.on('message', (raw) => {
        let msg;
        try { msg = JSON.parse(raw.toString()); } catch { return; }
        const { type, requestId } = msg || {};

        // --- Mgmt handshake reply ---
        if (type === 'mgmt.reply' && msg.pub && msg.nonce && ws.mgmt) {
            try {
                const clientPub = Buffer.from(msg.pub, 'base64');
                const shared = ecdh.computeSecret(clientPub);
                const clientNonce = Buffer.from(msg.nonce, 'base64');
                ws.mgmt.sessionKey = hkdfLike(shared, ws.mgmt.serverNonce, clientNonce);
                console.log('Mgmt handshake ready for extension');
                ws.send(JSON.stringify({ type: 'mgmt.ready' }));
            } catch (e) {
                console.error('Mgmt handshake failed:', e);
            }
            return;
        }
        // --- Mgmt encrypted commands ---
        if (type === 'mgmt.enc' && ws.mgmt?.sessionKey && msg.payload) {
            try {
                const inner = decMgmt(msg.payload, ws.mgmt.sessionKey);
                const { op, reqId, keys } = inner || {};
                const respond = (obj) => ws.send(JSON.stringify({ type: 'mgmt.enc.resp', payload: encMgmt({ reqId, ...obj }, ws.mgmt.sessionKey) }));

                // Prune expired keys before any op
                for (const [k, meta] of API_KEYS) {
                    if (Date.now() > meta.createdAt + meta.ttlMs) API_KEYS.delete(k);
                }

                if (op === 'keys.list') {
                    const list = getKeysArrayFromMap(API_KEYS).map(r => ({ key: r.key, createdAt: r.createdAt, expiresAt: r.createdAt + r.ttlMs }));
                    return respond({ ok: true, data: { keys: list, limit: MAX_KEYS } });
                }
                if (op === 'keys.create') {
                    if (API_KEYS.size >= MAX_KEYS) return respond({ ok: false, error: 'limit_reached' });
                    const created = createApiKey();
                    const list = getKeysArrayFromMap(API_KEYS).map(r => ({ key: r.key, createdAt: r.createdAt, expiresAt: r.createdAt + r.ttlMs }));
                    return respond({ ok: true, data: { created, keys: list, limit: MAX_KEYS } });
                }
                if (op === 'keys.delete') {
                    if (Array.isArray(keys)) {
                        for (const k of keys) API_KEYS.delete(k);
                        saveKeysToStore(getKeysArrayFromMap(API_KEYS));
                    }
                    const list = getKeysArrayFromMap(API_KEYS).map(r => ({ key: r.key, createdAt: r.createdAt, expiresAt: r.createdAt + r.ttlMs }));
                    return respond({ ok: true, data: { keys: list, limit: MAX_KEYS } });
                }
                return respond({ ok: false, error: 'unknown_op' });
            } catch (e) {
                console.error('Mgmt enc handling failed:', e);
            }
            return;
        }

        if (type === 'hello') {
            // ignore
            return;
        }
        if (type === 'response.models' && requestId && WAITERS.has(requestId)) {
            const w = WAITERS.get(requestId);
            clearTimeout(w.timeout);
            WAITERS.delete(requestId);
            return w.resolve({ models: msg.models || [] });
        }
        if (type === 'response.result' && requestId && WAITERS.has(requestId)) {
            const w = WAITERS.get(requestId);
            clearTimeout(w.timeout);
            WAITERS.delete(requestId);
            return w.resolve(msg.result || {});
        }
        if (type === 'response.error' && requestId) {
            if (WAITERS.has(requestId)) {
                const w = WAITERS.get(requestId);
                clearTimeout(w.timeout);
                WAITERS.delete(requestId);
                const err = new Error(msg.error?.message || 'Unknown error');
                if (msg.error?.code) err.code = msg.error.code;
                return w.reject(err);
            }
            if (STREAM_HANDLERS.has(requestId)) {
                const h = STREAM_HANDLERS.get(requestId);
                h.onError?.(msg.error);
                STREAM_HANDLERS.delete(requestId);
            }
            return;
        }
        if (type === 'response.chunk' && requestId && STREAM_HANDLERS.has(requestId)) {
            const h = STREAM_HANDLERS.get(requestId);
            h.onChunk?.(msg.data || {});
            return;
        }
        if (type === 'response.end' && requestId && STREAM_HANDLERS.has(requestId)) {
            const h = STREAM_HANDLERS.get(requestId);
            h.onEnd?.();
            STREAM_HANDLERS.delete(requestId);
            return;
        }
    });

    ws.on('close', () => {
        console.log('Extension WS disconnected');
        extensionClient = null;
        // Reject any pending non-stream requests
        for (const [_id, w] of WAITERS) {
            clearTimeout(w.timeout);
            w.reject(new Error('Extension disconnected'));
        }
        WAITERS.clear();
        // Complete any streams with error
        for (const [_id, h] of STREAM_HANDLERS) {
            h.onError?.(new Error('Extension disconnected'));
        }
        STREAM_HANDLERS.clear();
    });
});

function sendAndWait(ws, requestId, msg, timeoutMs = 10000) {
    return new Promise((resolve, reject) => {
        const timeout = setTimeout(() => {
            WAITERS.delete(requestId);
            reject(new Error('Timeout waiting for extension response'));
        }, timeoutMs);
        WAITERS.set(requestId, { resolve, reject, timeout });
        ws.send(JSON.stringify(msg), (err) => {
            if (err) {
                clearTimeout(timeout);
                WAITERS.delete(requestId);
                reject(err);
            }
        });
    });
}

function registerResponseHandlers(requestId, handlers) {
    STREAM_HANDLERS.set(requestId, handlers);
    return () => {
        STREAM_HANDLERS.delete(requestId);
    };
}

function mapErrorCodeToOpenAIType(code) {
    switch (code) {
        case 'unauthorized': return 'authentication_error';
        case 'invalid_request': return 'invalid_request_error';
        case 'rate_limit_exceeded': return 'rate_limit_error';
        case 'service_unavailable': return 'server_error';
        case 'network_error': return 'server_error';
        default: return 'server_error';
    }
}

function mapErrorCodeToStatus(code) {
    switch (code) {
        case 'unauthorized': return 401;
        case 'invalid_request': return 400;
        case 'rate_limit_exceeded': return 429;
        case 'service_unavailable': return 503;
        case 'network_error': return 502;
        default: return 502;
    }
}

