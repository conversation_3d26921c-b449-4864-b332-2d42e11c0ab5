<!DOCTYPE html>
<meta charset="UTF-8">
<meta name="viewport" content="width=device-width, initial-scale=1.0">
<html lang="en">

<head>
  <title>AI Models Bridge Demo</title>
  <link rel="stylesheet" href="styles.css">
</head>

<body>
  <h2>AI Chat Extension</h2>

  <select id="model-select" title="Select AI Model">
    <option value="gemini-web">Bard/Gemini Web</option>
    <option value="bing-web">Bing/Copilot Web</option>
    <option value="claude-web">Claude Web</option>
    <option value="perplexity-web">Perplexity Web</option>
    <option value="deepseek-web">Deepseek Web</option>
  </select>

  <div id="api-key-container">
    <input type="password" id="api-key" placeholder="Enter API Key">
  </div>

  <!-- Add Claude styles dropdown (hidden by default) -->
  <div id="claude-styles-container" class="hidden">
    <select id="claude-style-select" title="Select Claude Style">
      <option value="">Default Style</option>
      <!-- Styles will be populated dynamically -->
    </select>
  </div>

  <!-- Add Perplexity options (hidden by default) -->
  <div id="perplexity-options-container" class="hidden">
    <div class="option-group">
      <label for="perplexity-model-select">Perplexity Model:</label>
      <select id="perplexity-model-select" title="Select Perplexity Model">
        <!-- Models will be populated dynamically -->
      </select>
    </div>

    <div id="perplexity-sources-container" class="option-group">
      <label>Search Sources:</label>
      <div id="perplexity-sources-checkboxes">
        <!-- Sources will be populated dynamically -->
      </div>
    </div>
  </div>

  <div class="button-row">
    <button id="new-thread-btn" class="btn">New Conversation</button>
    <button id="load-conversations" class="btn">Load Conversations</button>
  </div>

  <div id="conversations-list" class="hidden">
    <!-- Conversations will be listed here -->
  </div>

  <div id="thread-title-container">
    <div id="current-thread-title"></div>
    <button id="edit-title-btn" class="icon-btn" title="Edit title">✏️</button>
  </div>

  <div id="response">
    <div id="chat-container">
      <!-- Messages will appear here -->
    </div>
  </div>

  <!-- Removed the old global reasoning container -->
  <div class="image-upload-container">
    <div class="file-upload-row">
      <span class="attachments-label">Attachments</span>
      <input type="file" id="file-upload-btn" multiple>
    </div>
    <div id="image-preview"></div>
  </div>

  <textarea id="prompt" placeholder="Enter your message" rows="3"></textarea>

  <button id="send-button">Send</button>
  <script src="lib/ai-models-bridge.min.js"></script>
  <script src="dist/popup.bundle.js"></script>
</body>

</html>