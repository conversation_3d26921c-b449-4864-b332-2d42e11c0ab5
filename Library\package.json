{"name": "ai-models-bridge", "version": "0.1.0", "description": "A library for interacting with various AI models", "main": "dist/index.js", "types": "dist/index.d.ts", "type": "module", "scripts": {"build": "tsc", "bundle": "rollup -c rollup.config.js", "build:all": "npm install && npm run build && npm run bundle", "copy-to-demo": "node scripts/copy-to-demo.js", "build:demo": "npm install && npm run build && npm run bundle && npm run copy-to-demo", "build:demoA": "npm run build && npm run bundle && npm run copy-to-demo", "test": "jest", "prepublishOnly": "npm run build:all"}, "keywords": ["ai", "chatbot", "llm", "chatgpt", "gemini", "claude"], "author": "<PERSON><PERSON><PERSON>", "license": "MIT", "dependencies": {"isomorphic-ws": "^5.0.0", "ofetch": "^1.4.1", "uuid": "^9.0.0", "webextension-polyfill": "^0.10.0"}, "devDependencies": {"@rollup/plugin-commonjs": "^28.0.6", "@rollup/plugin-node-resolve": "^16.0.1", "@rollup/plugin-terser": "^0.4.4", "@rollup/plugin-typescript": "^12.1.4", "@types/uuid": "^9.0.0", "@types/webextension-polyfill": "^0.10.0", "jest": "^29.7.0", "rollup": "^4.46.2", "tslib": "^2.8.1", "typescript": "^5.0.0"}}